import { useState, useEffect, useCallback, useMemo } from 'react';

// Custom hook for performance monitoring
export const usePerformance = () => {
  const [metrics, setMetrics] = useState({
    loadTime: 0,
    renderTime: 0,
    memoryUsage: 0,
    networkRequests: 0
  });

  useEffect(() => {
    // Monitor page load time
    const loadTime = performance.now();
    setMetrics(prev => ({ ...prev, loadTime }));

    // Monitor memory usage (if available)
    if ('memory' in performance) {
      const memoryUsage = performance.memory.usedJSHeapSize / 1024 / 1024; // MB
      setMetrics(prev => ({ ...prev, memoryUsage }));
    }

    // Monitor network requests
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const networkRequests = entries.filter(entry => 
        entry.entryType === 'navigation' || entry.entryType === 'resource'
      ).length;
      
      setMetrics(prev => ({ ...prev, networkRequests: prev.networkRequests + networkRequests }));
    });

    observer.observe({ entryTypes: ['navigation', 'resource'] });

    return () => observer.disconnect();
  }, []);

  const measureRenderTime = useCallback((componentName, renderFn) => {
    const startTime = performance.now();
    const result = renderFn();
    const endTime = performance.now();
    
    console.log(`${componentName} render time: ${endTime - startTime}ms`);
    setMetrics(prev => ({ ...prev, renderTime: endTime - startTime }));
    
    return result;
  }, []);

  return { metrics, measureRenderTime };
};

// Custom hook for debouncing values
export const useDebounce = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

// Custom hook for throttling functions
export const useThrottle = (callback, delay) => {
  const [lastRun, setLastRun] = useState(Date.now());

  return useCallback((...args) => {
    if (Date.now() - lastRun >= delay) {
      callback(...args);
      setLastRun(Date.now());
    }
  }, [callback, delay, lastRun]);
};

// Custom hook for lazy loading images
export const useLazyImage = (src, placeholder = '') => {
  const [imageSrc, setImageSrc] = useState(placeholder);
  const [isLoaded, setIsLoaded] = useState(false);
  const [isError, setIsError] = useState(false);

  useEffect(() => {
    const img = new Image();
    
    img.onload = () => {
      setImageSrc(src);
      setIsLoaded(true);
    };
    
    img.onerror = () => {
      setIsError(true);
    };
    
    img.src = src;
  }, [src]);

  return { imageSrc, isLoaded, isError };
};

// Custom hook for virtual scrolling
export const useVirtualScroll = (items, itemHeight, containerHeight) => {
  const [scrollTop, setScrollTop] = useState(0);

  const visibleItems = useMemo(() => {
    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight) + 1,
      items.length
    );

    return {
      startIndex,
      endIndex,
      items: items.slice(startIndex, endIndex),
      totalHeight: items.length * itemHeight,
      offsetY: startIndex * itemHeight
    };
  }, [items, itemHeight, containerHeight, scrollTop]);

  const handleScroll = useCallback((e) => {
    setScrollTop(e.target.scrollTop);
  }, []);

  return { visibleItems, handleScroll };
};

// Custom hook for caching API responses
export const useCache = (key, fetcher, options = {}) => {
  const { ttl = 5 * 60 * 1000, enabled = true } = options; // 5 minutes default TTL
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const getCachedData = useCallback(() => {
    if (!enabled) return null;
    
    try {
      const cached = localStorage.getItem(`cache_${key}`);
      if (cached) {
        const { data, timestamp } = JSON.parse(cached);
        if (Date.now() - timestamp < ttl) {
          return data;
        }
      }
    } catch (e) {
      console.warn('Cache read error:', e);
    }
    return null;
  }, [key, ttl, enabled]);

  const setCachedData = useCallback((data) => {
    if (!enabled) return;
    
    try {
      localStorage.setItem(`cache_${key}`, JSON.stringify({
        data,
        timestamp: Date.now()
      }));
    } catch (e) {
      console.warn('Cache write error:', e);
    }
  }, [key, enabled]);

  useEffect(() => {
    const cachedData = getCachedData();
    if (cachedData) {
      setData(cachedData);
      return;
    }

    if (!fetcher) return;

    setLoading(true);
    setError(null);

    fetcher()
      .then((result) => {
        setData(result);
        setCachedData(result);
      })
      .catch((err) => {
        setError(err);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [key, fetcher, getCachedData, setCachedData]);

  const invalidateCache = useCallback(() => {
    localStorage.removeItem(`cache_${key}`);
  }, [key]);

  return { data, loading, error, invalidateCache };
};

// Custom hook for intersection observer (lazy loading)
export const useIntersectionObserver = (options = {}) => {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [ref, setRef] = useState(null);

  useEffect(() => {
    if (!ref) return;

    const observer = new IntersectionObserver(([entry]) => {
      setIsIntersecting(entry.isIntersecting);
    }, options);

    observer.observe(ref);

    return () => observer.disconnect();
  }, [ref, options]);

  return [setRef, isIntersecting];
};

// Performance monitoring component
export const PerformanceMonitor = ({ children }) => {
  const { metrics } = usePerformance();

  useEffect(() => {
    // Log performance metrics in development
    if (process.env.NODE_ENV === 'development') {
      console.group('Performance Metrics');
      console.log('Load Time:', `${metrics.loadTime.toFixed(2)}ms`);
      console.log('Memory Usage:', `${metrics.memoryUsage.toFixed(2)}MB`);
      console.log('Network Requests:', metrics.networkRequests);
      console.groupEnd();
    }
  }, [metrics]);

  return children;
};
