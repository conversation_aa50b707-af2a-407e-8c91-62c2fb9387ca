import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Paper,
  Typography,
  TextField,
  IconButton,
  List,
  ListItem,
  Avatar,
  Chip,
  Divider,
  Menu,
  MenuItem,
  Tooltip,
  Badge,
  InputAdornment
} from '@mui/material';
import {
  Send as SendIcon,
  EmojiEmotions as EmojiIcon,
  AttachFile as AttachIcon,
  MoreVert as MoreIcon,
  Reply as ReplyIcon
} from '@mui/icons-material';
import { formatDistanceToNow } from 'date-fns';
import { useAuth } from '../../context/AuthContext';
import { io } from 'socket.io-client';

const ChatRoom = ({ courseId, roomId, roomName }) => {
  const { user } = useAuth();
  const [socket, setSocket] = useState(null);
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [typingUsers, setTypingUsers] = useState([]);
  const [replyTo, setReplyTo] = useState(null);
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedMessage, setSelectedMessage] = useState(null);
  const messagesEndRef = useRef(null);
  const typingTimeoutRef = useRef(null);

  // Initialize socket connection
  useEffect(() => {
    const newSocket = io(import.meta.env.VITE_API_URL || 'http://localhost:5000');
    setSocket(newSocket);

    // Join course room
    newSocket.emit('join-course', courseId);

    // Listen for new messages
    newSocket.on('new-message', (message) => {
      setMessages(prev => [...prev, message]);
    });

    // Listen for typing indicators
    newSocket.on('user-typing', (data) => {
      if (data.user.id !== user.id) {
        setTypingUsers(prev => {
          if (data.isTyping) {
            return [...prev.filter(u => u.id !== data.user.id), data.user];
          } else {
            return prev.filter(u => u.id !== data.user.id);
          }
        });
      }
    });

    return () => {
      newSocket.close();
    };
  }, [courseId, user.id]);

  // Scroll to bottom when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Load initial messages
  useEffect(() => {
    loadMessages();
  }, [roomId]);

  const loadMessages = async () => {
    try {
      // In a real app, fetch messages from API
      const sampleMessages = [
        {
          id: 1,
          content: 'Welcome to the course discussion!',
          user: { id: 'instructor', firstName: 'John', lastName: 'Doe', avatar: null },
          timestamp: new Date(Date.now() - 1000 * 60 * 60),
          messageType: 'system'
        },
        {
          id: 2,
          content: 'Hi everyone! Excited to start learning.',
          user: { id: 'student1', firstName: 'Alice', lastName: 'Smith', avatar: null },
          timestamp: new Date(Date.now() - 1000 * 60 * 30),
          messageType: 'text'
        },
        {
          id: 3,
          content: 'Great to have you all here! Feel free to ask any questions.',
          user: { id: 'instructor', firstName: 'John', lastName: 'Doe', avatar: null },
          timestamp: new Date(Date.now() - 1000 * 60 * 15),
          messageType: 'text'
        }
      ];
      setMessages(sampleMessages);
    } catch (error) {
      console.error('Error loading messages:', error);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = () => {
    if (!newMessage.trim() || !socket) return;

    const messageData = {
      courseId,
      message: newMessage,
      user: {
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        avatar: user.avatar
      },
      replyTo: replyTo?.id || null
    };

    socket.emit('send-message', messageData);
    setNewMessage('');
    setReplyTo(null);
    handleStopTyping();
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleTyping = () => {
    if (!isTyping && socket) {
      setIsTyping(true);
      socket.emit('typing', {
        courseId,
        user: {
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName
        },
        isTyping: true
      });
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout
    typingTimeoutRef.current = setTimeout(() => {
      handleStopTyping();
    }, 2000);
  };

  const handleStopTyping = () => {
    if (isTyping && socket) {
      setIsTyping(false);
      socket.emit('typing', {
        courseId,
        user: {
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName
        },
        isTyping: false
      });
    }
  };

  const handleMessageMenu = (event, message) => {
    setAnchorEl(event.currentTarget);
    setSelectedMessage(message);
  };

  const handleCloseMenu = () => {
    setAnchorEl(null);
    setSelectedMessage(null);
  };

  const handleReply = () => {
    setReplyTo(selectedMessage);
    handleCloseMenu();
  };

  const formatMessageTime = (timestamp) => {
    return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
  };

  const renderMessage = (message) => {
    const isOwnMessage = message.user.id === user.id;
    const isSystemMessage = message.messageType === 'system';

    if (isSystemMessage) {
      return (
        <Box key={message.id} sx={{ textAlign: 'center', my: 2 }}>
          <Chip
            label={message.content}
            size="small"
            variant="outlined"
            color="primary"
          />
        </Box>
      );
    }

    return (
      <ListItem
        key={message.id}
        sx={{
          flexDirection: 'column',
          alignItems: isOwnMessage ? 'flex-end' : 'flex-start',
          py: 1
        }}
      >
        <Box
          sx={{
            display: 'flex',
            alignItems: 'flex-start',
            flexDirection: isOwnMessage ? 'row-reverse' : 'row',
            width: '100%',
            maxWidth: '70%'
          }}
        >
          <Avatar
            src={message.user.avatar}
            sx={{ 
              width: 32, 
              height: 32,
              mx: 1,
              fontSize: '0.8rem'
            }}
          >
            {message.user.firstName?.[0]}{message.user.lastName?.[0]}
          </Avatar>
          
          <Box sx={{ flex: 1, minWidth: 0 }}>
            <Box
              sx={{
                bgcolor: isOwnMessage ? 'primary.main' : 'grey.100',
                color: isOwnMessage ? 'white' : 'text.primary',
                borderRadius: 2,
                p: 1.5,
                position: 'relative'
              }}
            >
              {replyTo && message.replyTo === replyTo.id && (
                <Box
                  sx={{
                    borderLeft: 3,
                    borderColor: 'divider',
                    pl: 1,
                    mb: 1,
                    opacity: 0.7
                  }}
                >
                  <Typography variant="caption">
                    Replying to {replyTo.user.firstName}
                  </Typography>
                  <Typography variant="body2" noWrap>
                    {replyTo.content}
                  </Typography>
                </Box>
              )}
              
              <Typography variant="body2">
                {message.content}
              </Typography>
              
              <IconButton
                size="small"
                onClick={(e) => handleMessageMenu(e, message)}
                sx={{
                  position: 'absolute',
                  top: -8,
                  right: -8,
                  bgcolor: 'background.paper',
                  '&:hover': { bgcolor: 'background.paper' }
                }}
              >
                <MoreIcon fontSize="small" />
              </IconButton>
            </Box>
            
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: isOwnMessage ? 'flex-end' : 'flex-start',
                mt: 0.5,
                px: 1
              }}
            >
              <Typography variant="caption" color="text.secondary">
                {message.user.firstName} {message.user.lastName} • {formatMessageTime(message.timestamp)}
              </Typography>
            </Box>
          </Box>
        </Box>
      </ListItem>
    );
  };

  return (
    <Paper sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant="h6">{roomName}</Typography>
        <Typography variant="body2" color="text.secondary">
          {messages.length} messages
        </Typography>
      </Box>

      {/* Messages */}
      <Box sx={{ flex: 1, overflow: 'auto', p: 1 }}>
        <List>
          {messages.map(renderMessage)}
          <div ref={messagesEndRef} />
        </List>

        {/* Typing indicator */}
        {typingUsers.length > 0 && (
          <Box sx={{ px: 2, py: 1 }}>
            <Typography variant="caption" color="text.secondary">
              {typingUsers.map(u => u.firstName).join(', ')} {typingUsers.length === 1 ? 'is' : 'are'} typing...
            </Typography>
          </Box>
        )}
      </Box>

      {/* Reply indicator */}
      {replyTo && (
        <Box sx={{ px: 2, py: 1, bgcolor: 'grey.50', borderTop: 1, borderColor: 'divider' }}>
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <Typography variant="caption">
              Replying to {replyTo.user.firstName}: {replyTo.content.substring(0, 50)}...
            </Typography>
            <IconButton size="small" onClick={() => setReplyTo(null)}>
              ×
            </IconButton>
          </Box>
        </Box>
      )}

      {/* Message input */}
      <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
        <TextField
          fullWidth
          multiline
          maxRows={3}
          value={newMessage}
          onChange={(e) => {
            setNewMessage(e.target.value);
            handleTyping();
          }}
          onKeyPress={handleKeyPress}
          placeholder="Type a message..."
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <IconButton size="small">
                  <AttachIcon />
                </IconButton>
                <IconButton size="small">
                  <EmojiIcon />
                </IconButton>
              </InputAdornment>
            ),
            endAdornment: (
              <InputAdornment position="end">
                <IconButton
                  onClick={handleSendMessage}
                  disabled={!newMessage.trim()}
                  color="primary"
                >
                  <SendIcon />
                </IconButton>
              </InputAdornment>
            )
          }}
        />
      </Box>

      {/* Message menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleCloseMenu}
      >
        <MenuItem onClick={handleReply}>
          <ReplyIcon sx={{ mr: 1 }} />
          Reply
        </MenuItem>
        <MenuItem onClick={handleCloseMenu}>
          <EmojiIcon sx={{ mr: 1 }} />
          React
        </MenuItem>
      </Menu>
    </Paper>
  );
};

export default ChatRoom;
