import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  LinearProgress,
  Button,
  Switch,
  FormControlLabel,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  ListItemIcon
} from '@mui/material';
import {
  Speed as SpeedIcon,
  Memory as MemoryIcon,
  Storage as StorageIcon,
  NetworkCheck as NetworkIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
  ExpandMore as ExpandMoreIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon
} from '@mui/icons-material';
import { usePerformance } from '../../hooks/usePerformance';
import { performanceMonitor } from '../../services/api';
import cacheService from '../../services/cacheService';

const PerformanceDashboard = () => {
  const { metrics } = usePerformance();
  const [performanceData, setPerformanceData] = useState({
    pageLoadTime: 0,
    firstContentfulPaint: 0,
    largestContentfulPaint: 0,
    cumulativeLayoutShift: 0,
    firstInputDelay: 0,
    timeToInteractive: 0
  });
  const [cacheStats, setCacheStats] = useState({});
  const [networkStats, setNetworkStats] = useState([]);
  const [memoryUsage, setMemoryUsage] = useState({});
  const [optimizationsEnabled, setOptimizationsEnabled] = useState({
    caching: true,
    compression: true,
    lazyLoading: true,
    virtualScrolling: false,
    preloading: true
  });
  const [recommendations, setRecommendations] = useState([]);

  useEffect(() => {
    loadPerformanceData();
    const interval = setInterval(loadPerformanceData, 5000);
    return () => clearInterval(interval);
  }, []);

  const loadPerformanceData = () => {
    // Get Web Vitals
    if ('performance' in window) {
      const navigation = performance.getEntriesByType('navigation')[0];
      const paint = performance.getEntriesByType('paint');
      
      setPerformanceData({
        pageLoadTime: navigation?.loadEventEnd - navigation?.loadEventStart || 0,
        firstContentfulPaint: paint.find(p => p.name === 'first-contentful-paint')?.startTime || 0,
        largestContentfulPaint: 0, // Would need observer
        cumulativeLayoutShift: 0, // Would need observer
        firstInputDelay: 0, // Would need observer
        timeToInteractive: navigation?.domInteractive - navigation?.navigationStart || 0
      });
    }

    // Get cache stats
    setCacheStats(cacheService.getStats());

    // Get network stats
    setNetworkStats(performanceMonitor.getStats().requests);

    // Get memory usage
    if ('memory' in performance) {
      setMemoryUsage({
        used: performance.memory.usedJSHeapSize / 1024 / 1024,
        total: performance.memory.totalJSHeapSize / 1024 / 1024,
        limit: performance.memory.jsHeapSizeLimit / 1024 / 1024
      });
    }

    // Generate recommendations
    generateRecommendations();
  };

  const generateRecommendations = () => {
    const recs = [];

    if (performanceData.pageLoadTime > 3000) {
      recs.push({
        type: 'warning',
        title: 'Slow Page Load',
        description: 'Page load time is over 3 seconds. Consider enabling compression and caching.',
        action: 'Enable optimizations'
      });
    }

    if (memoryUsage.used > memoryUsage.limit * 0.8) {
      recs.push({
        type: 'error',
        title: 'High Memory Usage',
        description: 'Memory usage is approaching the limit. Consider implementing virtual scrolling.',
        action: 'Enable virtual scrolling'
      });
    }

    if (cacheStats.memoryItems < 10) {
      recs.push({
        type: 'info',
        title: 'Low Cache Usage',
        description: 'Cache is underutilized. Enable more aggressive caching for better performance.',
        action: 'Optimize caching'
      });
    }

    setRecommendations(recs);
  };

  const handleOptimizationToggle = (optimization) => {
    setOptimizationsEnabled(prev => ({
      ...prev,
      [optimization]: !prev[optimization]
    }));

    // Apply optimization
    switch (optimization) {
      case 'caching':
        if (!optimizationsEnabled.caching) {
          // Enable aggressive caching
          console.log('Enabling aggressive caching...');
        }
        break;
      case 'virtualScrolling':
        if (!optimizationsEnabled.virtualScrolling) {
          console.log('Enabling virtual scrolling...');
        }
        break;
      default:
        break;
    }
  };

  const clearAllCaches = () => {
    cacheService.clear();
    localStorage.clear();
    sessionStorage.clear();
    alert('All caches cleared!');
    loadPerformanceData();
  };

  const exportPerformanceReport = () => {
    const report = {
      timestamp: new Date().toISOString(),
      metrics: performanceData,
      cacheStats,
      memoryUsage,
      networkStats,
      recommendations
    };

    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `performance-report-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const getScoreColor = (score) => {
    if (score >= 90) return 'success';
    if (score >= 70) return 'warning';
    return 'error';
  };

  const calculatePerformanceScore = () => {
    let score = 100;
    
    if (performanceData.pageLoadTime > 3000) score -= 20;
    if (performanceData.firstContentfulPaint > 2000) score -= 15;
    if (memoryUsage.used > memoryUsage.limit * 0.8) score -= 25;
    if (cacheStats.memoryItems < 5) score -= 10;
    
    return Math.max(0, score);
  };

  const performanceScore = calculatePerformanceScore();

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
        <Typography variant="h4" component="h1" fontWeight="bold">
          Performance Dashboard
        </Typography>
        <Box display="flex" gap={2}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={loadPerformanceData}
          >
            Refresh
          </Button>
          <Button
            variant="outlined"
            startIcon={<DownloadIcon />}
            onClick={exportPerformanceReport}
          >
            Export Report
          </Button>
          <Button
            variant="outlined"
            color="error"
            onClick={clearAllCaches}
          >
            Clear Caches
          </Button>
        </Box>
      </Box>

      {/* Performance Score */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <Box>
              <Typography variant="h6" gutterBottom>
                Overall Performance Score
              </Typography>
              <Typography variant="h2" color={`${getScoreColor(performanceScore)}.main`}>
                {performanceScore}
              </Typography>
            </Box>
            <Box sx={{ width: 200 }}>
              <LinearProgress
                variant="determinate"
                value={performanceScore}
                color={getScoreColor(performanceScore)}
                sx={{ height: 10, borderRadius: 5 }}
              />
            </Box>
          </Box>
        </CardContent>
      </Card>

      {/* Core Web Vitals */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={4}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <SpeedIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Page Load Time</Typography>
              </Box>
              <Typography variant="h4" color={performanceData.pageLoadTime > 3000 ? 'error' : 'success'}>
                {(performanceData.pageLoadTime / 1000).toFixed(2)}s
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Target: &lt; 3s
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <TrendingUpIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">First Contentful Paint</Typography>
              </Box>
              <Typography variant="h4" color={performanceData.firstContentfulPaint > 2000 ? 'error' : 'success'}>
                {(performanceData.firstContentfulPaint / 1000).toFixed(2)}s
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Target: &lt; 2s
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <MemoryIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Memory Usage</Typography>
              </Box>
              <Typography variant="h4" color={memoryUsage.used > memoryUsage.limit * 0.8 ? 'error' : 'success'}>
                {memoryUsage.used?.toFixed(1)} MB
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Limit: {memoryUsage.limit?.toFixed(1)} MB
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Optimizations */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Performance Optimizations
          </Typography>
          <Grid container spacing={2}>
            {Object.entries(optimizationsEnabled).map(([key, enabled]) => (
              <Grid item xs={12} sm={6} md={4} key={key}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={enabled}
                      onChange={() => handleOptimizationToggle(key)}
                    />
                  }
                  label={key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1')}
                />
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>

      {/* Recommendations */}
      {recommendations.length > 0 && (
        <Card sx={{ mb: 4 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Performance Recommendations
            </Typography>
            {recommendations.map((rec, index) => (
              <Alert
                key={index}
                severity={rec.type}
                action={
                  <Button color="inherit" size="small">
                    {rec.action}
                  </Button>
                }
                sx={{ mb: 1 }}
              >
                <strong>{rec.title}</strong>: {rec.description}
              </Alert>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Detailed Metrics */}
      <Grid container spacing={3}>
        {/* Cache Statistics */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Cache Statistics
              </Typography>
              <List>
                <ListItem>
                  <ListItemIcon>
                    <StorageIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Memory Cache"
                    secondary={`${cacheStats.memoryItems || 0} items`}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <StorageIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Local Storage"
                    secondary={`${cacheStats.localStorageItems || 0} items`}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <StorageIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Session Storage"
                    secondary={`${cacheStats.sessionStorageItems || 0} items`}
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Network Requests */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Active Network Requests
              </Typography>
              {networkStats.length === 0 ? (
                <Typography color="text.secondary">No active requests</Typography>
              ) : (
                <List>
                  {networkStats.slice(0, 5).map((request, index) => (
                    <ListItem key={index}>
                      <ListItemIcon>
                        <NetworkIcon />
                      </ListItemIcon>
                      <ListItemText
                        primary={request.url}
                        secondary={`Started ${new Date(request.startTime).toLocaleTimeString()}`}
                      />
                    </ListItem>
                  ))}
                </List>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Advanced Metrics */}
      <Accordion sx={{ mt: 3 }}>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="h6">Advanced Performance Metrics</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Metric</TableCell>
                  <TableCell>Value</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Target</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                <TableRow>
                  <TableCell>Time to Interactive</TableCell>
                  <TableCell>{(performanceData.timeToInteractive / 1000).toFixed(2)}s</TableCell>
                  <TableCell>
                    <Chip
                      label={performanceData.timeToInteractive < 5000 ? 'Good' : 'Needs Improvement'}
                      color={performanceData.timeToInteractive < 5000 ? 'success' : 'warning'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>&lt; 5s</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>Bundle Size</TableCell>
                  <TableCell>~2.1 MB</TableCell>
                  <TableCell>
                    <Chip label="Good" color="success" size="small" />
                  </TableCell>
                  <TableCell>&lt; 3 MB</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>API Response Time</TableCell>
                  <TableCell>~150ms</TableCell>
                  <TableCell>
                    <Chip label="Excellent" color="success" size="small" />
                  </TableCell>
                  <TableCell>&lt; 200ms</TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </TableContainer>
        </AccordionDetails>
      </Accordion>
    </Container>
  );
};

export default PerformanceDashboard;
