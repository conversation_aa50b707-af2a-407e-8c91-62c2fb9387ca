{"version": 3, "sources": ["../../react-player/lib/players/FilePlayer.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar FilePlayer_exports = {};\n__export(FilePlayer_exports, {\n  default: () => FilePlayer\n});\nmodule.exports = __toCommonJS(FilePlayer_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_utils = require(\"../utils\");\nvar import_patterns = require(\"../patterns\");\nconst HAS_NAVIGATOR = typeof navigator !== \"undefined\";\nconst IS_IPAD_PRO = HAS_NAVIGATOR && navigator.platform === \"MacIntel\" && navigator.maxTouchPoints > 1;\nconst IS_IOS = HAS_NAVIGATOR && (/iPad|iPhone|iPod/.test(navigator.userAgent) || IS_IPAD_PRO) && !window.MSStream;\nconst IS_SAFARI = HAS_NAVIGATOR && /^((?!chrome|android).)*safari/i.test(navigator.userAgent) && !window.MSStream;\nconst HLS_SDK_URL = \"https://cdn.jsdelivr.net/npm/hls.js@VERSION/dist/hls.min.js\";\nconst HLS_GLOBAL = \"Hls\";\nconst DASH_SDK_URL = \"https://cdnjs.cloudflare.com/ajax/libs/dashjs/VERSION/dash.all.min.js\";\nconst DASH_GLOBAL = \"dashjs\";\nconst FLV_SDK_URL = \"https://cdn.jsdelivr.net/npm/flv.js@VERSION/dist/flv.min.js\";\nconst FLV_GLOBAL = \"flvjs\";\nconst MATCH_DROPBOX_URL = /www\\.dropbox\\.com\\/.+/;\nconst MATCH_CLOUDFLARE_STREAM = /https:\\/\\/watch\\.cloudflarestream\\.com\\/([a-z0-9]+)/;\nconst REPLACE_CLOUDFLARE_STREAM = \"https://videodelivery.net/{id}/manifest/video.m3u8\";\nclass FilePlayer extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    // Proxy methods to prevent listener leaks\n    __publicField(this, \"onReady\", (...args) => this.props.onReady(...args));\n    __publicField(this, \"onPlay\", (...args) => this.props.onPlay(...args));\n    __publicField(this, \"onBuffer\", (...args) => this.props.onBuffer(...args));\n    __publicField(this, \"onBufferEnd\", (...args) => this.props.onBufferEnd(...args));\n    __publicField(this, \"onPause\", (...args) => this.props.onPause(...args));\n    __publicField(this, \"onEnded\", (...args) => this.props.onEnded(...args));\n    __publicField(this, \"onError\", (...args) => this.props.onError(...args));\n    __publicField(this, \"onPlayBackRateChange\", (event) => this.props.onPlaybackRateChange(event.target.playbackRate));\n    __publicField(this, \"onEnablePIP\", (...args) => this.props.onEnablePIP(...args));\n    __publicField(this, \"onDisablePIP\", (e) => {\n      const { onDisablePIP, playing } = this.props;\n      onDisablePIP(e);\n      if (playing) {\n        this.play();\n      }\n    });\n    __publicField(this, \"onPresentationModeChange\", (e) => {\n      if (this.player && (0, import_utils.supportsWebKitPresentationMode)(this.player)) {\n        const { webkitPresentationMode } = this.player;\n        if (webkitPresentationMode === \"picture-in-picture\") {\n          this.onEnablePIP(e);\n        } else if (webkitPresentationMode === \"inline\") {\n          this.onDisablePIP(e);\n        }\n      }\n    });\n    __publicField(this, \"onSeek\", (e) => {\n      this.props.onSeek(e.target.currentTime);\n    });\n    __publicField(this, \"mute\", () => {\n      this.player.muted = true;\n    });\n    __publicField(this, \"unmute\", () => {\n      this.player.muted = false;\n    });\n    __publicField(this, \"renderSourceElement\", (source, index) => {\n      if (typeof source === \"string\") {\n        return /* @__PURE__ */ import_react.default.createElement(\"source\", { key: index, src: source });\n      }\n      return /* @__PURE__ */ import_react.default.createElement(\"source\", { key: index, ...source });\n    });\n    __publicField(this, \"renderTrack\", (track, index) => {\n      return /* @__PURE__ */ import_react.default.createElement(\"track\", { key: index, ...track });\n    });\n    __publicField(this, \"ref\", (player) => {\n      if (this.player) {\n        this.prevPlayer = this.player;\n      }\n      this.player = player;\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n    this.addListeners(this.player);\n    const src = this.getSource(this.props.url);\n    if (src) {\n      this.player.src = src;\n    }\n    if (IS_IOS || this.props.config.forceDisableHls) {\n      this.player.load();\n    }\n  }\n  componentDidUpdate(prevProps) {\n    if (this.shouldUseAudio(this.props) !== this.shouldUseAudio(prevProps)) {\n      this.removeListeners(this.prevPlayer, prevProps.url);\n      this.addListeners(this.player);\n    }\n    if (this.props.url !== prevProps.url && !(0, import_utils.isMediaStream)(this.props.url) && !(this.props.url instanceof Array)) {\n      this.player.srcObject = null;\n    }\n  }\n  componentWillUnmount() {\n    this.player.removeAttribute(\"src\");\n    this.removeListeners(this.player);\n    if (this.hls) {\n      this.hls.destroy();\n    }\n  }\n  addListeners(player) {\n    const { url, playsinline } = this.props;\n    player.addEventListener(\"play\", this.onPlay);\n    player.addEventListener(\"waiting\", this.onBuffer);\n    player.addEventListener(\"playing\", this.onBufferEnd);\n    player.addEventListener(\"pause\", this.onPause);\n    player.addEventListener(\"seeked\", this.onSeek);\n    player.addEventListener(\"ended\", this.onEnded);\n    player.addEventListener(\"error\", this.onError);\n    player.addEventListener(\"ratechange\", this.onPlayBackRateChange);\n    player.addEventListener(\"enterpictureinpicture\", this.onEnablePIP);\n    player.addEventListener(\"leavepictureinpicture\", this.onDisablePIP);\n    player.addEventListener(\"webkitpresentationmodechanged\", this.onPresentationModeChange);\n    if (!this.shouldUseHLS(url)) {\n      player.addEventListener(\"canplay\", this.onReady);\n    }\n    if (playsinline) {\n      player.setAttribute(\"playsinline\", \"\");\n      player.setAttribute(\"webkit-playsinline\", \"\");\n      player.setAttribute(\"x5-playsinline\", \"\");\n    }\n  }\n  removeListeners(player, url) {\n    player.removeEventListener(\"canplay\", this.onReady);\n    player.removeEventListener(\"play\", this.onPlay);\n    player.removeEventListener(\"waiting\", this.onBuffer);\n    player.removeEventListener(\"playing\", this.onBufferEnd);\n    player.removeEventListener(\"pause\", this.onPause);\n    player.removeEventListener(\"seeked\", this.onSeek);\n    player.removeEventListener(\"ended\", this.onEnded);\n    player.removeEventListener(\"error\", this.onError);\n    player.removeEventListener(\"ratechange\", this.onPlayBackRateChange);\n    player.removeEventListener(\"enterpictureinpicture\", this.onEnablePIP);\n    player.removeEventListener(\"leavepictureinpicture\", this.onDisablePIP);\n    player.removeEventListener(\"webkitpresentationmodechanged\", this.onPresentationModeChange);\n    if (!this.shouldUseHLS(url)) {\n      player.removeEventListener(\"canplay\", this.onReady);\n    }\n  }\n  shouldUseAudio(props) {\n    if (props.config.forceVideo) {\n      return false;\n    }\n    if (props.config.attributes.poster) {\n      return false;\n    }\n    return import_patterns.AUDIO_EXTENSIONS.test(props.url) || props.config.forceAudio;\n  }\n  shouldUseHLS(url) {\n    if (IS_SAFARI && this.props.config.forceSafariHLS || this.props.config.forceHLS) {\n      return true;\n    }\n    if (IS_IOS || this.props.config.forceDisableHls) {\n      return false;\n    }\n    return import_patterns.HLS_EXTENSIONS.test(url) || MATCH_CLOUDFLARE_STREAM.test(url);\n  }\n  shouldUseDASH(url) {\n    return import_patterns.DASH_EXTENSIONS.test(url) || this.props.config.forceDASH;\n  }\n  shouldUseFLV(url) {\n    return import_patterns.FLV_EXTENSIONS.test(url) || this.props.config.forceFLV;\n  }\n  load(url) {\n    const { hlsVersion, hlsOptions, dashVersion, flvVersion } = this.props.config;\n    if (this.hls) {\n      this.hls.destroy();\n    }\n    if (this.dash) {\n      this.dash.reset();\n    }\n    if (this.shouldUseHLS(url)) {\n      (0, import_utils.getSDK)(HLS_SDK_URL.replace(\"VERSION\", hlsVersion), HLS_GLOBAL).then((Hls) => {\n        this.hls = new Hls(hlsOptions);\n        this.hls.on(Hls.Events.MANIFEST_PARSED, () => {\n          this.props.onReady();\n        });\n        this.hls.on(Hls.Events.ERROR, (e, data) => {\n          this.props.onError(e, data, this.hls, Hls);\n        });\n        if (MATCH_CLOUDFLARE_STREAM.test(url)) {\n          const id = url.match(MATCH_CLOUDFLARE_STREAM)[1];\n          this.hls.loadSource(REPLACE_CLOUDFLARE_STREAM.replace(\"{id}\", id));\n        } else {\n          this.hls.loadSource(url);\n        }\n        this.hls.attachMedia(this.player);\n        this.props.onLoaded();\n      });\n    }\n    if (this.shouldUseDASH(url)) {\n      (0, import_utils.getSDK)(DASH_SDK_URL.replace(\"VERSION\", dashVersion), DASH_GLOBAL).then((dashjs) => {\n        this.dash = dashjs.MediaPlayer().create();\n        this.dash.initialize(this.player, url, this.props.playing);\n        this.dash.on(\"error\", this.props.onError);\n        if (parseInt(dashVersion) < 3) {\n          this.dash.getDebug().setLogToBrowserConsole(false);\n        } else {\n          this.dash.updateSettings({ debug: { logLevel: dashjs.Debug.LOG_LEVEL_NONE } });\n        }\n        this.props.onLoaded();\n      });\n    }\n    if (this.shouldUseFLV(url)) {\n      (0, import_utils.getSDK)(FLV_SDK_URL.replace(\"VERSION\", flvVersion), FLV_GLOBAL).then((flvjs) => {\n        this.flv = flvjs.createPlayer({ type: \"flv\", url });\n        this.flv.attachMediaElement(this.player);\n        this.flv.on(flvjs.Events.ERROR, (e, data) => {\n          this.props.onError(e, data, this.flv, flvjs);\n        });\n        this.flv.load();\n        this.props.onLoaded();\n      });\n    }\n    if (url instanceof Array) {\n      this.player.load();\n    } else if ((0, import_utils.isMediaStream)(url)) {\n      try {\n        this.player.srcObject = url;\n      } catch (e) {\n        this.player.src = window.URL.createObjectURL(url);\n      }\n    }\n  }\n  play() {\n    const promise = this.player.play();\n    if (promise) {\n      promise.catch(this.props.onError);\n    }\n  }\n  pause() {\n    this.player.pause();\n  }\n  stop() {\n    this.player.removeAttribute(\"src\");\n    if (this.dash) {\n      this.dash.reset();\n    }\n  }\n  seekTo(seconds, keepPlaying = true) {\n    this.player.currentTime = seconds;\n    if (!keepPlaying) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n    this.player.volume = fraction;\n  }\n  enablePIP() {\n    if (this.player.requestPictureInPicture && document.pictureInPictureElement !== this.player) {\n      this.player.requestPictureInPicture();\n    } else if ((0, import_utils.supportsWebKitPresentationMode)(this.player) && this.player.webkitPresentationMode !== \"picture-in-picture\") {\n      this.player.webkitSetPresentationMode(\"picture-in-picture\");\n    }\n  }\n  disablePIP() {\n    if (document.exitPictureInPicture && document.pictureInPictureElement === this.player) {\n      document.exitPictureInPicture();\n    } else if ((0, import_utils.supportsWebKitPresentationMode)(this.player) && this.player.webkitPresentationMode !== \"inline\") {\n      this.player.webkitSetPresentationMode(\"inline\");\n    }\n  }\n  setPlaybackRate(rate) {\n    try {\n      this.player.playbackRate = rate;\n    } catch (error) {\n      this.props.onError(error);\n    }\n  }\n  getDuration() {\n    if (!this.player)\n      return null;\n    const { duration, seekable } = this.player;\n    if (duration === Infinity && seekable.length > 0) {\n      return seekable.end(seekable.length - 1);\n    }\n    return duration;\n  }\n  getCurrentTime() {\n    if (!this.player)\n      return null;\n    return this.player.currentTime;\n  }\n  getSecondsLoaded() {\n    if (!this.player)\n      return null;\n    const { buffered } = this.player;\n    if (buffered.length === 0) {\n      return 0;\n    }\n    const end = buffered.end(buffered.length - 1);\n    const duration = this.getDuration();\n    if (end > duration) {\n      return duration;\n    }\n    return end;\n  }\n  getSource(url) {\n    const useHLS = this.shouldUseHLS(url);\n    const useDASH = this.shouldUseDASH(url);\n    const useFLV = this.shouldUseFLV(url);\n    if (url instanceof Array || (0, import_utils.isMediaStream)(url) || useHLS || useDASH || useFLV) {\n      return void 0;\n    }\n    if (MATCH_DROPBOX_URL.test(url)) {\n      return url.replace(\"www.dropbox.com\", \"dl.dropboxusercontent.com\");\n    }\n    return url;\n  }\n  render() {\n    const { url, playing, loop, controls, muted, config, width, height } = this.props;\n    const useAudio = this.shouldUseAudio(this.props);\n    const Element = useAudio ? \"audio\" : \"video\";\n    const style = {\n      width: width === \"auto\" ? width : \"100%\",\n      height: height === \"auto\" ? height : \"100%\"\n    };\n    return /* @__PURE__ */ import_react.default.createElement(\n      Element,\n      {\n        ref: this.ref,\n        src: this.getSource(url),\n        style,\n        preload: \"auto\",\n        autoPlay: playing || void 0,\n        controls,\n        muted,\n        loop,\n        ...config.attributes\n      },\n      url instanceof Array && url.map(this.renderSourceElement),\n      config.tracks.map(this.renderTrack)\n    );\n  }\n}\n__publicField(FilePlayer, \"displayName\", \"FilePlayer\");\n__publicField(FilePlayer, \"canPlay\", import_patterns.canPlay.file);\n"], "mappings": ";;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,WAAW,OAAO;AACtB,QAAI,YAAY,OAAO;AACvB,QAAI,mBAAmB,OAAO;AAC9B,QAAI,oBAAoB,OAAO;AAC/B,QAAI,eAAe,OAAO;AAC1B,QAAI,eAAe,OAAO,UAAU;AACpC,QAAI,kBAAkB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,UAAU,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC1J,QAAI,WAAW,CAAC,QAAQ,QAAQ;AAC9B,eAAS,QAAQ;AACf,kBAAU,QAAQ,MAAM,EAAE,KAAK,IAAI,IAAI,GAAG,YAAY,KAAK,CAAC;AAAA,IAChE;AACA,QAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,UAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,iBAAS,OAAO,kBAAkB,IAAI;AACpC,cAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,sBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,MACvH;AACA,aAAO;AAAA,IACT;AACA,QAAI,UAAU,CAAC,KAAK,YAAY,YAAY,SAAS,OAAO,OAAO,SAAS,aAAa,GAAG,CAAC,IAAI,CAAC,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA,MAKnG,cAAc,CAAC,OAAO,CAAC,IAAI,aAAa,UAAU,QAAQ,WAAW,EAAE,OAAO,KAAK,YAAY,KAAK,CAAC,IAAI;AAAA,MACzG;AAAA,IACF;AACA,QAAI,eAAe,CAAC,QAAQ,YAAY,UAAU,CAAC,GAAG,cAAc,EAAE,OAAO,KAAK,CAAC,GAAG,GAAG;AACzF,QAAI,gBAAgB,CAAC,KAAK,KAAK,UAAU;AACvC,sBAAgB,KAAK,OAAO,QAAQ,WAAW,MAAM,KAAK,KAAK,KAAK;AACpE,aAAO;AAAA,IACT;AACA,QAAI,qBAAqB,CAAC;AAC1B,aAAS,oBAAoB;AAAA,MAC3B,SAAS,MAAM;AAAA,IACjB,CAAC;AACD,WAAO,UAAU,aAAa,kBAAkB;AAChD,QAAI,eAAe,QAAQ,eAAgB;AAC3C,QAAI,eAAe;AACnB,QAAI,kBAAkB;AACtB,QAAM,gBAAgB,OAAO,cAAc;AAC3C,QAAM,cAAc,iBAAiB,UAAU,aAAa,cAAc,UAAU,iBAAiB;AACrG,QAAM,SAAS,kBAAkB,mBAAmB,KAAK,UAAU,SAAS,KAAK,gBAAgB,CAAC,OAAO;AACzG,QAAM,YAAY,iBAAiB,iCAAiC,KAAK,UAAU,SAAS,KAAK,CAAC,OAAO;AACzG,QAAM,cAAc;AACpB,QAAM,aAAa;AACnB,QAAM,eAAe;AACrB,QAAM,cAAc;AACpB,QAAM,cAAc;AACpB,QAAM,aAAa;AACnB,QAAM,oBAAoB;AAC1B,QAAM,0BAA0B;AAChC,QAAM,4BAA4B;AAClC,QAAM,aAAN,cAAyB,aAAa,UAAU;AAAA,MAC9C,cAAc;AACZ,cAAM,GAAG,SAAS;AAElB,sBAAc,MAAM,WAAW,IAAI,SAAS,KAAK,MAAM,QAAQ,GAAG,IAAI,CAAC;AACvE,sBAAc,MAAM,UAAU,IAAI,SAAS,KAAK,MAAM,OAAO,GAAG,IAAI,CAAC;AACrE,sBAAc,MAAM,YAAY,IAAI,SAAS,KAAK,MAAM,SAAS,GAAG,IAAI,CAAC;AACzE,sBAAc,MAAM,eAAe,IAAI,SAAS,KAAK,MAAM,YAAY,GAAG,IAAI,CAAC;AAC/E,sBAAc,MAAM,WAAW,IAAI,SAAS,KAAK,MAAM,QAAQ,GAAG,IAAI,CAAC;AACvE,sBAAc,MAAM,WAAW,IAAI,SAAS,KAAK,MAAM,QAAQ,GAAG,IAAI,CAAC;AACvE,sBAAc,MAAM,WAAW,IAAI,SAAS,KAAK,MAAM,QAAQ,GAAG,IAAI,CAAC;AACvE,sBAAc,MAAM,wBAAwB,CAAC,UAAU,KAAK,MAAM,qBAAqB,MAAM,OAAO,YAAY,CAAC;AACjH,sBAAc,MAAM,eAAe,IAAI,SAAS,KAAK,MAAM,YAAY,GAAG,IAAI,CAAC;AAC/E,sBAAc,MAAM,gBAAgB,CAAC,MAAM;AACzC,gBAAM,EAAE,cAAc,QAAQ,IAAI,KAAK;AACvC,uBAAa,CAAC;AACd,cAAI,SAAS;AACX,iBAAK,KAAK;AAAA,UACZ;AAAA,QACF,CAAC;AACD,sBAAc,MAAM,4BAA4B,CAAC,MAAM;AACrD,cAAI,KAAK,WAAW,GAAG,aAAa,gCAAgC,KAAK,MAAM,GAAG;AAChF,kBAAM,EAAE,uBAAuB,IAAI,KAAK;AACxC,gBAAI,2BAA2B,sBAAsB;AACnD,mBAAK,YAAY,CAAC;AAAA,YACpB,WAAW,2BAA2B,UAAU;AAC9C,mBAAK,aAAa,CAAC;AAAA,YACrB;AAAA,UACF;AAAA,QACF,CAAC;AACD,sBAAc,MAAM,UAAU,CAAC,MAAM;AACnC,eAAK,MAAM,OAAO,EAAE,OAAO,WAAW;AAAA,QACxC,CAAC;AACD,sBAAc,MAAM,QAAQ,MAAM;AAChC,eAAK,OAAO,QAAQ;AAAA,QACtB,CAAC;AACD,sBAAc,MAAM,UAAU,MAAM;AAClC,eAAK,OAAO,QAAQ;AAAA,QACtB,CAAC;AACD,sBAAc,MAAM,uBAAuB,CAAC,QAAQ,UAAU;AAC5D,cAAI,OAAO,WAAW,UAAU;AAC9B,mBAAuB,aAAa,QAAQ,cAAc,UAAU,EAAE,KAAK,OAAO,KAAK,OAAO,CAAC;AAAA,UACjG;AACA,iBAAuB,aAAa,QAAQ,cAAc,UAAU,EAAE,KAAK,OAAO,GAAG,OAAO,CAAC;AAAA,QAC/F,CAAC;AACD,sBAAc,MAAM,eAAe,CAAC,OAAO,UAAU;AACnD,iBAAuB,aAAa,QAAQ,cAAc,SAAS,EAAE,KAAK,OAAO,GAAG,MAAM,CAAC;AAAA,QAC7F,CAAC;AACD,sBAAc,MAAM,OAAO,CAAC,WAAW;AACrC,cAAI,KAAK,QAAQ;AACf,iBAAK,aAAa,KAAK;AAAA,UACzB;AACA,eAAK,SAAS;AAAA,QAChB,CAAC;AAAA,MACH;AAAA,MACA,oBAAoB;AAClB,aAAK,MAAM,WAAW,KAAK,MAAM,QAAQ,IAAI;AAC7C,aAAK,aAAa,KAAK,MAAM;AAC7B,cAAM,MAAM,KAAK,UAAU,KAAK,MAAM,GAAG;AACzC,YAAI,KAAK;AACP,eAAK,OAAO,MAAM;AAAA,QACpB;AACA,YAAI,UAAU,KAAK,MAAM,OAAO,iBAAiB;AAC/C,eAAK,OAAO,KAAK;AAAA,QACnB;AAAA,MACF;AAAA,MACA,mBAAmB,WAAW;AAC5B,YAAI,KAAK,eAAe,KAAK,KAAK,MAAM,KAAK,eAAe,SAAS,GAAG;AACtE,eAAK,gBAAgB,KAAK,YAAY,UAAU,GAAG;AACnD,eAAK,aAAa,KAAK,MAAM;AAAA,QAC/B;AACA,YAAI,KAAK,MAAM,QAAQ,UAAU,OAAO,EAAE,GAAG,aAAa,eAAe,KAAK,MAAM,GAAG,KAAK,EAAE,KAAK,MAAM,eAAe,QAAQ;AAC9H,eAAK,OAAO,YAAY;AAAA,QAC1B;AAAA,MACF;AAAA,MACA,uBAAuB;AACrB,aAAK,OAAO,gBAAgB,KAAK;AACjC,aAAK,gBAAgB,KAAK,MAAM;AAChC,YAAI,KAAK,KAAK;AACZ,eAAK,IAAI,QAAQ;AAAA,QACnB;AAAA,MACF;AAAA,MACA,aAAa,QAAQ;AACnB,cAAM,EAAE,KAAK,YAAY,IAAI,KAAK;AAClC,eAAO,iBAAiB,QAAQ,KAAK,MAAM;AAC3C,eAAO,iBAAiB,WAAW,KAAK,QAAQ;AAChD,eAAO,iBAAiB,WAAW,KAAK,WAAW;AACnD,eAAO,iBAAiB,SAAS,KAAK,OAAO;AAC7C,eAAO,iBAAiB,UAAU,KAAK,MAAM;AAC7C,eAAO,iBAAiB,SAAS,KAAK,OAAO;AAC7C,eAAO,iBAAiB,SAAS,KAAK,OAAO;AAC7C,eAAO,iBAAiB,cAAc,KAAK,oBAAoB;AAC/D,eAAO,iBAAiB,yBAAyB,KAAK,WAAW;AACjE,eAAO,iBAAiB,yBAAyB,KAAK,YAAY;AAClE,eAAO,iBAAiB,iCAAiC,KAAK,wBAAwB;AACtF,YAAI,CAAC,KAAK,aAAa,GAAG,GAAG;AAC3B,iBAAO,iBAAiB,WAAW,KAAK,OAAO;AAAA,QACjD;AACA,YAAI,aAAa;AACf,iBAAO,aAAa,eAAe,EAAE;AACrC,iBAAO,aAAa,sBAAsB,EAAE;AAC5C,iBAAO,aAAa,kBAAkB,EAAE;AAAA,QAC1C;AAAA,MACF;AAAA,MACA,gBAAgB,QAAQ,KAAK;AAC3B,eAAO,oBAAoB,WAAW,KAAK,OAAO;AAClD,eAAO,oBAAoB,QAAQ,KAAK,MAAM;AAC9C,eAAO,oBAAoB,WAAW,KAAK,QAAQ;AACnD,eAAO,oBAAoB,WAAW,KAAK,WAAW;AACtD,eAAO,oBAAoB,SAAS,KAAK,OAAO;AAChD,eAAO,oBAAoB,UAAU,KAAK,MAAM;AAChD,eAAO,oBAAoB,SAAS,KAAK,OAAO;AAChD,eAAO,oBAAoB,SAAS,KAAK,OAAO;AAChD,eAAO,oBAAoB,cAAc,KAAK,oBAAoB;AAClE,eAAO,oBAAoB,yBAAyB,KAAK,WAAW;AACpE,eAAO,oBAAoB,yBAAyB,KAAK,YAAY;AACrE,eAAO,oBAAoB,iCAAiC,KAAK,wBAAwB;AACzF,YAAI,CAAC,KAAK,aAAa,GAAG,GAAG;AAC3B,iBAAO,oBAAoB,WAAW,KAAK,OAAO;AAAA,QACpD;AAAA,MACF;AAAA,MACA,eAAe,OAAO;AACpB,YAAI,MAAM,OAAO,YAAY;AAC3B,iBAAO;AAAA,QACT;AACA,YAAI,MAAM,OAAO,WAAW,QAAQ;AAClC,iBAAO;AAAA,QACT;AACA,eAAO,gBAAgB,iBAAiB,KAAK,MAAM,GAAG,KAAK,MAAM,OAAO;AAAA,MAC1E;AAAA,MACA,aAAa,KAAK;AAChB,YAAI,aAAa,KAAK,MAAM,OAAO,kBAAkB,KAAK,MAAM,OAAO,UAAU;AAC/E,iBAAO;AAAA,QACT;AACA,YAAI,UAAU,KAAK,MAAM,OAAO,iBAAiB;AAC/C,iBAAO;AAAA,QACT;AACA,eAAO,gBAAgB,eAAe,KAAK,GAAG,KAAK,wBAAwB,KAAK,GAAG;AAAA,MACrF;AAAA,MACA,cAAc,KAAK;AACjB,eAAO,gBAAgB,gBAAgB,KAAK,GAAG,KAAK,KAAK,MAAM,OAAO;AAAA,MACxE;AAAA,MACA,aAAa,KAAK;AAChB,eAAO,gBAAgB,eAAe,KAAK,GAAG,KAAK,KAAK,MAAM,OAAO;AAAA,MACvE;AAAA,MACA,KAAK,KAAK;AACR,cAAM,EAAE,YAAY,YAAY,aAAa,WAAW,IAAI,KAAK,MAAM;AACvE,YAAI,KAAK,KAAK;AACZ,eAAK,IAAI,QAAQ;AAAA,QACnB;AACA,YAAI,KAAK,MAAM;AACb,eAAK,KAAK,MAAM;AAAA,QAClB;AACA,YAAI,KAAK,aAAa,GAAG,GAAG;AAC1B,WAAC,GAAG,aAAa,QAAQ,YAAY,QAAQ,WAAW,UAAU,GAAG,UAAU,EAAE,KAAK,CAAC,QAAQ;AAC7F,iBAAK,MAAM,IAAI,IAAI,UAAU;AAC7B,iBAAK,IAAI,GAAG,IAAI,OAAO,iBAAiB,MAAM;AAC5C,mBAAK,MAAM,QAAQ;AAAA,YACrB,CAAC;AACD,iBAAK,IAAI,GAAG,IAAI,OAAO,OAAO,CAAC,GAAG,SAAS;AACzC,mBAAK,MAAM,QAAQ,GAAG,MAAM,KAAK,KAAK,GAAG;AAAA,YAC3C,CAAC;AACD,gBAAI,wBAAwB,KAAK,GAAG,GAAG;AACrC,oBAAM,KAAK,IAAI,MAAM,uBAAuB,EAAE,CAAC;AAC/C,mBAAK,IAAI,WAAW,0BAA0B,QAAQ,QAAQ,EAAE,CAAC;AAAA,YACnE,OAAO;AACL,mBAAK,IAAI,WAAW,GAAG;AAAA,YACzB;AACA,iBAAK,IAAI,YAAY,KAAK,MAAM;AAChC,iBAAK,MAAM,SAAS;AAAA,UACtB,CAAC;AAAA,QACH;AACA,YAAI,KAAK,cAAc,GAAG,GAAG;AAC3B,WAAC,GAAG,aAAa,QAAQ,aAAa,QAAQ,WAAW,WAAW,GAAG,WAAW,EAAE,KAAK,CAAC,WAAW;AACnG,iBAAK,OAAO,OAAO,YAAY,EAAE,OAAO;AACxC,iBAAK,KAAK,WAAW,KAAK,QAAQ,KAAK,KAAK,MAAM,OAAO;AACzD,iBAAK,KAAK,GAAG,SAAS,KAAK,MAAM,OAAO;AACxC,gBAAI,SAAS,WAAW,IAAI,GAAG;AAC7B,mBAAK,KAAK,SAAS,EAAE,uBAAuB,KAAK;AAAA,YACnD,OAAO;AACL,mBAAK,KAAK,eAAe,EAAE,OAAO,EAAE,UAAU,OAAO,MAAM,eAAe,EAAE,CAAC;AAAA,YAC/E;AACA,iBAAK,MAAM,SAAS;AAAA,UACtB,CAAC;AAAA,QACH;AACA,YAAI,KAAK,aAAa,GAAG,GAAG;AAC1B,WAAC,GAAG,aAAa,QAAQ,YAAY,QAAQ,WAAW,UAAU,GAAG,UAAU,EAAE,KAAK,CAAC,UAAU;AAC/F,iBAAK,MAAM,MAAM,aAAa,EAAE,MAAM,OAAO,IAAI,CAAC;AAClD,iBAAK,IAAI,mBAAmB,KAAK,MAAM;AACvC,iBAAK,IAAI,GAAG,MAAM,OAAO,OAAO,CAAC,GAAG,SAAS;AAC3C,mBAAK,MAAM,QAAQ,GAAG,MAAM,KAAK,KAAK,KAAK;AAAA,YAC7C,CAAC;AACD,iBAAK,IAAI,KAAK;AACd,iBAAK,MAAM,SAAS;AAAA,UACtB,CAAC;AAAA,QACH;AACA,YAAI,eAAe,OAAO;AACxB,eAAK,OAAO,KAAK;AAAA,QACnB,YAAY,GAAG,aAAa,eAAe,GAAG,GAAG;AAC/C,cAAI;AACF,iBAAK,OAAO,YAAY;AAAA,UAC1B,SAAS,GAAG;AACV,iBAAK,OAAO,MAAM,OAAO,IAAI,gBAAgB,GAAG;AAAA,UAClD;AAAA,QACF;AAAA,MACF;AAAA,MACA,OAAO;AACL,cAAM,UAAU,KAAK,OAAO,KAAK;AACjC,YAAI,SAAS;AACX,kBAAQ,MAAM,KAAK,MAAM,OAAO;AAAA,QAClC;AAAA,MACF;AAAA,MACA,QAAQ;AACN,aAAK,OAAO,MAAM;AAAA,MACpB;AAAA,MACA,OAAO;AACL,aAAK,OAAO,gBAAgB,KAAK;AACjC,YAAI,KAAK,MAAM;AACb,eAAK,KAAK,MAAM;AAAA,QAClB;AAAA,MACF;AAAA,MACA,OAAO,SAAS,cAAc,MAAM;AAClC,aAAK,OAAO,cAAc;AAC1B,YAAI,CAAC,aAAa;AAChB,eAAK,MAAM;AAAA,QACb;AAAA,MACF;AAAA,MACA,UAAU,UAAU;AAClB,aAAK,OAAO,SAAS;AAAA,MACvB;AAAA,MACA,YAAY;AACV,YAAI,KAAK,OAAO,2BAA2B,SAAS,4BAA4B,KAAK,QAAQ;AAC3F,eAAK,OAAO,wBAAwB;AAAA,QACtC,YAAY,GAAG,aAAa,gCAAgC,KAAK,MAAM,KAAK,KAAK,OAAO,2BAA2B,sBAAsB;AACvI,eAAK,OAAO,0BAA0B,oBAAoB;AAAA,QAC5D;AAAA,MACF;AAAA,MACA,aAAa;AACX,YAAI,SAAS,wBAAwB,SAAS,4BAA4B,KAAK,QAAQ;AACrF,mBAAS,qBAAqB;AAAA,QAChC,YAAY,GAAG,aAAa,gCAAgC,KAAK,MAAM,KAAK,KAAK,OAAO,2BAA2B,UAAU;AAC3H,eAAK,OAAO,0BAA0B,QAAQ;AAAA,QAChD;AAAA,MACF;AAAA,MACA,gBAAgB,MAAM;AACpB,YAAI;AACF,eAAK,OAAO,eAAe;AAAA,QAC7B,SAAS,OAAO;AACd,eAAK,MAAM,QAAQ,KAAK;AAAA,QAC1B;AAAA,MACF;AAAA,MACA,cAAc;AACZ,YAAI,CAAC,KAAK;AACR,iBAAO;AACT,cAAM,EAAE,UAAU,SAAS,IAAI,KAAK;AACpC,YAAI,aAAa,YAAY,SAAS,SAAS,GAAG;AAChD,iBAAO,SAAS,IAAI,SAAS,SAAS,CAAC;AAAA,QACzC;AACA,eAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AACf,YAAI,CAAC,KAAK;AACR,iBAAO;AACT,eAAO,KAAK,OAAO;AAAA,MACrB;AAAA,MACA,mBAAmB;AACjB,YAAI,CAAC,KAAK;AACR,iBAAO;AACT,cAAM,EAAE,SAAS,IAAI,KAAK;AAC1B,YAAI,SAAS,WAAW,GAAG;AACzB,iBAAO;AAAA,QACT;AACA,cAAM,MAAM,SAAS,IAAI,SAAS,SAAS,CAAC;AAC5C,cAAM,WAAW,KAAK,YAAY;AAClC,YAAI,MAAM,UAAU;AAClB,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAAA,MACA,UAAU,KAAK;AACb,cAAM,SAAS,KAAK,aAAa,GAAG;AACpC,cAAM,UAAU,KAAK,cAAc,GAAG;AACtC,cAAM,SAAS,KAAK,aAAa,GAAG;AACpC,YAAI,eAAe,UAAU,GAAG,aAAa,eAAe,GAAG,KAAK,UAAU,WAAW,QAAQ;AAC/F,iBAAO;AAAA,QACT;AACA,YAAI,kBAAkB,KAAK,GAAG,GAAG;AAC/B,iBAAO,IAAI,QAAQ,mBAAmB,2BAA2B;AAAA,QACnE;AACA,eAAO;AAAA,MACT;AAAA,MACA,SAAS;AACP,cAAM,EAAE,KAAK,SAAS,MAAM,UAAU,OAAO,QAAQ,OAAO,OAAO,IAAI,KAAK;AAC5E,cAAM,WAAW,KAAK,eAAe,KAAK,KAAK;AAC/C,cAAM,UAAU,WAAW,UAAU;AACrC,cAAM,QAAQ;AAAA,UACZ,OAAO,UAAU,SAAS,QAAQ;AAAA,UAClC,QAAQ,WAAW,SAAS,SAAS;AAAA,QACvC;AACA,eAAuB,aAAa,QAAQ;AAAA,UAC1C;AAAA,UACA;AAAA,YACE,KAAK,KAAK;AAAA,YACV,KAAK,KAAK,UAAU,GAAG;AAAA,YACvB;AAAA,YACA,SAAS;AAAA,YACT,UAAU,WAAW;AAAA,YACrB;AAAA,YACA;AAAA,YACA;AAAA,YACA,GAAG,OAAO;AAAA,UACZ;AAAA,UACA,eAAe,SAAS,IAAI,IAAI,KAAK,mBAAmB;AAAA,UACxD,OAAO,OAAO,IAAI,KAAK,WAAW;AAAA,QACpC;AAAA,MACF;AAAA,IACF;AACA,kBAAc,YAAY,eAAe,YAAY;AACrD,kBAAc,YAAY,WAAW,gBAAgB,QAAQ,IAAI;AAAA;AAAA;", "names": []}