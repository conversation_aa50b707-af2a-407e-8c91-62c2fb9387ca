const mongoose = require('mongoose');

const questionSchema = new mongoose.Schema({
  question: {
    type: String,
    required: [true, 'Question text is required'],
    trim: true
  },
  type: {
    type: String,
    required: true,
    enum: ['multiple-choice', 'true-false', 'short-answer', 'essay']
  },
  options: [{
    text: {
      type: String,
      required: true
    },
    isCorrect: {
      type: Boolean,
      default: false
    }
  }],
  correctAnswer: {
    type: String, // For short-answer and essay questions
    trim: true
  },
  points: {
    type: Number,
    required: true,
    min: [1, 'Points must be at least 1']
  },
  explanation: {
    type: String,
    trim: true
  }
});

const assessmentSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Assessment title is required'],
    trim: true,
    maxlength: [100, 'Assessment title cannot exceed 100 characters']
  },
  description: {
    type: String,
    required: [true, 'Assessment description is required'],
    maxlength: [500, 'Assessment description cannot exceed 500 characters']
  },
  course: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Course',
    required: true
  },
  instructor: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  type: {
    type: String,
    required: true,
    enum: ['quiz', 'assignment', 'exam']
  },
  questions: [questionSchema],
  timeLimit: {
    type: Number, // in minutes
    default: null
  },
  attempts: {
    type: Number,
    default: 1,
    min: [1, 'At least 1 attempt must be allowed']
  },
  passingScore: {
    type: Number,
    required: true,
    min: [0, 'Passing score cannot be negative'],
    max: [100, 'Passing score cannot exceed 100']
  },
  totalPoints: {
    type: Number,
    default: 0
  },
  isPublished: {
    type: Boolean,
    default: false
  },
  dueDate: {
    type: Date
  },
  availableFrom: {
    type: Date,
    default: Date.now
  },
  availableUntil: {
    type: Date
  },
  shuffleQuestions: {
    type: Boolean,
    default: false
  },
  showCorrectAnswers: {
    type: Boolean,
    default: true
  },
  showScoreImmediately: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Calculate total points before saving
assessmentSchema.pre('save', function(next) {
  if (this.questions && this.questions.length > 0) {
    this.totalPoints = this.questions.reduce((total, question) => {
      return total + question.points;
    }, 0);
  }
  next();
});

// Indexes for better query performance
assessmentSchema.index({ course: 1 });
assessmentSchema.index({ instructor: 1 });
assessmentSchema.index({ type: 1 });
assessmentSchema.index({ dueDate: 1 });

module.exports = mongoose.model('Assessment', assessmentSchema);
