{"version": 3, "sources": ["../../load-script/index.js", "../../deepmerge/dist/cjs.js", "../../react-player/lib/utils.js", "../../react-player/lib/patterns.js"], "sourcesContent": ["\nmodule.exports = function load (src, opts, cb) {\n  var head = document.head || document.getElementsByTagName('head')[0]\n  var script = document.createElement('script')\n\n  if (typeof opts === 'function') {\n    cb = opts\n    opts = {}\n  }\n\n  opts = opts || {}\n  cb = cb || function() {}\n\n  script.type = opts.type || 'text/javascript'\n  script.charset = opts.charset || 'utf8';\n  script.async = 'async' in opts ? !!opts.async : true\n  script.src = src\n\n  if (opts.attrs) {\n    setAttributes(script, opts.attrs)\n  }\n\n  if (opts.text) {\n    script.text = '' + opts.text\n  }\n\n  var onend = 'onload' in script ? stdOnEnd : ieOnEnd\n  onend(script, cb)\n\n  // some good legacy browsers (firefox) fail the 'in' detection above\n  // so as a fallback we always set onload\n  // old IE will ignore this and new IE will set onload\n  if (!script.onload) {\n    stdOnEnd(script, cb);\n  }\n\n  head.appendChild(script)\n}\n\nfunction setAttributes(script, attrs) {\n  for (var attr in attrs) {\n    script.setAttribute(attr, attrs[attr]);\n  }\n}\n\nfunction stdOnEnd (script, cb) {\n  script.onload = function () {\n    this.onerror = this.onload = null\n    cb(null, script)\n  }\n  script.onerror = function () {\n    // this.onload = null here is necessary\n    // because even IE9 works not like others\n    this.onerror = this.onload = null\n    cb(new Error('Failed to load ' + this.src), script)\n  }\n}\n\nfunction ieOnEnd (script, cb) {\n  script.onreadystatechange = function () {\n    if (this.readyState != 'complete' && this.readyState != 'loaded') return\n    this.onreadystatechange = null\n    cb(null, script) // there is no way to catch loading errors in IE8\n  }\n}\n", "'use strict';\n\nvar isMergeableObject = function isMergeableObject(value) {\n\treturn isNonNullObject(value)\n\t\t&& !isSpecial(value)\n};\n\nfunction isNonNullObject(value) {\n\treturn !!value && typeof value === 'object'\n}\n\nfunction isSpecial(value) {\n\tvar stringValue = Object.prototype.toString.call(value);\n\n\treturn stringValue === '[object RegExp]'\n\t\t|| stringValue === '[object Date]'\n\t\t|| isReactElement(value)\n}\n\n// see https://github.com/facebook/react/blob/b5ac963fb791d1298e7f396236383bc955f916c1/src/isomorphic/classic/element/ReactElement.js#L21-L25\nvar canUseSymbol = typeof Symbol === 'function' && Symbol.for;\nvar REACT_ELEMENT_TYPE = canUseSymbol ? Symbol.for('react.element') : 0xeac7;\n\nfunction isReactElement(value) {\n\treturn value.$$typeof === REACT_ELEMENT_TYPE\n}\n\nfunction emptyTarget(val) {\n\treturn Array.isArray(val) ? [] : {}\n}\n\nfunction cloneUnlessOtherwiseSpecified(value, options) {\n\treturn (options.clone !== false && options.isMergeableObject(value))\n\t\t? deepmerge(emptyTarget(value), value, options)\n\t\t: value\n}\n\nfunction defaultArrayMerge(target, source, options) {\n\treturn target.concat(source).map(function(element) {\n\t\treturn cloneUnlessOtherwiseSpecified(element, options)\n\t})\n}\n\nfunction getMergeFunction(key, options) {\n\tif (!options.customMerge) {\n\t\treturn deepmerge\n\t}\n\tvar customMerge = options.customMerge(key);\n\treturn typeof customMerge === 'function' ? customMerge : deepmerge\n}\n\nfunction getEnumerableOwnPropertySymbols(target) {\n\treturn Object.getOwnPropertySymbols\n\t\t? Object.getOwnPropertySymbols(target).filter(function(symbol) {\n\t\t\treturn Object.propertyIsEnumerable.call(target, symbol)\n\t\t})\n\t\t: []\n}\n\nfunction getKeys(target) {\n\treturn Object.keys(target).concat(getEnumerableOwnPropertySymbols(target))\n}\n\nfunction propertyIsOnObject(object, property) {\n\ttry {\n\t\treturn property in object\n\t} catch(_) {\n\t\treturn false\n\t}\n}\n\n// Protects from prototype poisoning and unexpected merging up the prototype chain.\nfunction propertyIsUnsafe(target, key) {\n\treturn propertyIsOnObject(target, key) // Properties are safe to merge if they don't exist in the target yet,\n\t\t&& !(Object.hasOwnProperty.call(target, key) // unsafe if they exist up the prototype chain,\n\t\t\t&& Object.propertyIsEnumerable.call(target, key)) // and also unsafe if they're nonenumerable.\n}\n\nfunction mergeObject(target, source, options) {\n\tvar destination = {};\n\tif (options.isMergeableObject(target)) {\n\t\tgetKeys(target).forEach(function(key) {\n\t\t\tdestination[key] = cloneUnlessOtherwiseSpecified(target[key], options);\n\t\t});\n\t}\n\tgetKeys(source).forEach(function(key) {\n\t\tif (propertyIsUnsafe(target, key)) {\n\t\t\treturn\n\t\t}\n\n\t\tif (propertyIsOnObject(target, key) && options.isMergeableObject(source[key])) {\n\t\t\tdestination[key] = getMergeFunction(key, options)(target[key], source[key], options);\n\t\t} else {\n\t\t\tdestination[key] = cloneUnlessOtherwiseSpecified(source[key], options);\n\t\t}\n\t});\n\treturn destination\n}\n\nfunction deepmerge(target, source, options) {\n\toptions = options || {};\n\toptions.arrayMerge = options.arrayMerge || defaultArrayMerge;\n\toptions.isMergeableObject = options.isMergeableObject || isMergeableObject;\n\t// cloneUnlessOtherwiseSpecified is added to `options` so that custom arrayMerge()\n\t// implementations can use it. The caller may not replace it.\n\toptions.cloneUnlessOtherwiseSpecified = cloneUnlessOtherwiseSpecified;\n\n\tvar sourceIsArray = Array.isArray(source);\n\tvar targetIsArray = Array.isArray(target);\n\tvar sourceAndTargetTypesMatch = sourceIsArray === targetIsArray;\n\n\tif (!sourceAndTargetTypesMatch) {\n\t\treturn cloneUnlessOtherwiseSpecified(source, options)\n\t} else if (sourceIsArray) {\n\t\treturn options.arrayMerge(target, source, options)\n\t} else {\n\t\treturn mergeObject(target, source, options)\n\t}\n}\n\ndeepmerge.all = function deepmergeAll(array, options) {\n\tif (!Array.isArray(array)) {\n\t\tthrow new Error('first argument should be an array')\n\t}\n\n\treturn array.reduce(function(prev, next) {\n\t\treturn deepmerge(prev, next, options)\n\t}, {})\n};\n\nvar deepmerge_1 = deepmerge;\n\nmodule.exports = deepmerge_1;\n", "var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar utils_exports = {};\n__export(utils_exports, {\n  callPlayer: () => callPlayer,\n  getConfig: () => getConfig,\n  getSDK: () => getSDK,\n  isBlobUrl: () => isBlobUrl,\n  isMediaStream: () => isMediaStream,\n  lazy: () => lazy,\n  omit: () => omit,\n  parseEndTime: () => parseEndTime,\n  parseStartTime: () => parseStartTime,\n  queryString: () => queryString,\n  randomString: () => randomString,\n  supportsWebKitPresentationMode: () => supportsWebKitPresentationMode\n});\nmodule.exports = __toCommonJS(utils_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_load_script = __toESM(require(\"load-script\"));\nvar import_deepmerge = __toESM(require(\"deepmerge\"));\nconst lazy = (componentImportFn) => import_react.default.lazy(async () => {\n  const obj = await componentImportFn();\n  return typeof obj.default === \"function\" ? obj : obj.default;\n});\nconst MATCH_START_QUERY = /[?&#](?:start|t)=([0-9hms]+)/;\nconst MATCH_END_QUERY = /[?&#]end=([0-9hms]+)/;\nconst MATCH_START_STAMP = /(\\d+)(h|m|s)/g;\nconst MATCH_NUMERIC = /^\\d+$/;\nfunction parseTimeParam(url, pattern) {\n  if (url instanceof Array) {\n    return void 0;\n  }\n  const match = url.match(pattern);\n  if (match) {\n    const stamp = match[1];\n    if (stamp.match(MATCH_START_STAMP)) {\n      return parseTimeString(stamp);\n    }\n    if (MATCH_NUMERIC.test(stamp)) {\n      return parseInt(stamp);\n    }\n  }\n  return void 0;\n}\nfunction parseTimeString(stamp) {\n  let seconds = 0;\n  let array = MATCH_START_STAMP.exec(stamp);\n  while (array !== null) {\n    const [, count, period] = array;\n    if (period === \"h\")\n      seconds += parseInt(count, 10) * 60 * 60;\n    if (period === \"m\")\n      seconds += parseInt(count, 10) * 60;\n    if (period === \"s\")\n      seconds += parseInt(count, 10);\n    array = MATCH_START_STAMP.exec(stamp);\n  }\n  return seconds;\n}\nfunction parseStartTime(url) {\n  return parseTimeParam(url, MATCH_START_QUERY);\n}\nfunction parseEndTime(url) {\n  return parseTimeParam(url, MATCH_END_QUERY);\n}\nfunction randomString() {\n  return Math.random().toString(36).substr(2, 5);\n}\nfunction queryString(object) {\n  return Object.keys(object).map((key) => `${key}=${object[key]}`).join(\"&\");\n}\nfunction getGlobal(key) {\n  if (window[key]) {\n    return window[key];\n  }\n  if (window.exports && window.exports[key]) {\n    return window.exports[key];\n  }\n  if (window.module && window.module.exports && window.module.exports[key]) {\n    return window.module.exports[key];\n  }\n  return null;\n}\nconst requests = {};\nconst getSDK = enableStubOn(function getSDK2(url, sdkGlobal, sdkReady = null, isLoaded = () => true, fetchScript = import_load_script.default) {\n  const existingGlobal = getGlobal(sdkGlobal);\n  if (existingGlobal && isLoaded(existingGlobal)) {\n    return Promise.resolve(existingGlobal);\n  }\n  return new Promise((resolve, reject) => {\n    if (requests[url]) {\n      requests[url].push({ resolve, reject });\n      return;\n    }\n    requests[url] = [{ resolve, reject }];\n    const onLoaded = (sdk) => {\n      requests[url].forEach((request) => request.resolve(sdk));\n    };\n    if (sdkReady) {\n      const previousOnReady = window[sdkReady];\n      window[sdkReady] = function() {\n        if (previousOnReady)\n          previousOnReady();\n        onLoaded(getGlobal(sdkGlobal));\n      };\n    }\n    fetchScript(url, (err) => {\n      if (err) {\n        requests[url].forEach((request) => request.reject(err));\n        requests[url] = null;\n      } else if (!sdkReady) {\n        onLoaded(getGlobal(sdkGlobal));\n      }\n    });\n  });\n});\nfunction getConfig(props, defaultProps) {\n  return (0, import_deepmerge.default)(defaultProps.config, props.config);\n}\nfunction omit(object, ...arrays) {\n  const omitKeys = [].concat(...arrays);\n  const output = {};\n  const keys = Object.keys(object);\n  for (const key of keys) {\n    if (omitKeys.indexOf(key) === -1) {\n      output[key] = object[key];\n    }\n  }\n  return output;\n}\nfunction callPlayer(method, ...args) {\n  if (!this.player || !this.player[method]) {\n    let message = `ReactPlayer: ${this.constructor.displayName} player could not call %c${method}%c \\u2013 `;\n    if (!this.player) {\n      message += \"The player was not available\";\n    } else if (!this.player[method]) {\n      message += \"The method was not available\";\n    }\n    console.warn(message, \"font-weight: bold\", \"\");\n    return null;\n  }\n  return this.player[method](...args);\n}\nfunction isMediaStream(url) {\n  return typeof window !== \"undefined\" && typeof window.MediaStream !== \"undefined\" && url instanceof window.MediaStream;\n}\nfunction isBlobUrl(url) {\n  return /^blob:/.test(url);\n}\nfunction supportsWebKitPresentationMode(video = document.createElement(\"video\")) {\n  const notMobile = /iPhone|iPod/.test(navigator.userAgent) === false;\n  return video.webkitSupportsPresentationMode && typeof video.webkitSetPresentationMode === \"function\" && notMobile;\n}\nfunction enableStubOn(fn) {\n  if (false) {\n    const wrap = (...args) => wrap.stub(...args);\n    wrap.stub = fn;\n    return wrap;\n  }\n  return fn;\n}\n", "var __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar patterns_exports = {};\n__export(patterns_exports, {\n  AUDIO_EXTENSIONS: () => AUDIO_EXTENSIONS,\n  DASH_EXTENSIONS: () => DASH_EXTENSIONS,\n  FLV_EXTENSIONS: () => FLV_EXTENSIONS,\n  HLS_EXTENSIONS: () => HLS_EXTENSIONS,\n  MATCH_URL_DAILYMOTION: () => MATCH_URL_DAILYMOTION,\n  MATCH_URL_FACEBOOK: () => MATCH_URL_FACEBOOK,\n  MATCH_URL_FACEBOOK_WATCH: () => MATCH_URL_FACEBOOK_WATCH,\n  MATCH_URL_KALTURA: () => MATCH_URL_KALTURA,\n  MATCH_URL_MIXCLOUD: () => MATCH_URL_MIXCLOUD,\n  MATCH_URL_MUX: () => MATCH_URL_MUX,\n  MATCH_URL_SOUNDCLOUD: () => MATCH_URL_SOUNDCLOUD,\n  MATCH_URL_STREAMABLE: () => MATCH_URL_STREAMABLE,\n  MATCH_URL_TWITCH_CHANNEL: () => MATCH_URL_TWITCH_CHANNEL,\n  MATCH_URL_TWITCH_VIDEO: () => MATCH_URL_TWITCH_VIDEO,\n  MATCH_URL_VIDYARD: () => MATCH_URL_VIDYARD,\n  MATCH_URL_VIMEO: () => MATCH_URL_VIMEO,\n  MATCH_URL_WISTIA: () => MATCH_URL_WISTIA,\n  MATCH_URL_YOUTUBE: () => MATCH_URL_YOUTUBE,\n  VIDEO_EXTENSIONS: () => VIDEO_EXTENSIONS,\n  canPlay: () => canPlay\n});\nmodule.exports = __toCommonJS(patterns_exports);\nvar import_utils = require(\"./utils\");\nconst MATCH_URL_YOUTUBE = /(?:youtu\\.be\\/|youtube(?:-nocookie|education)?\\.com\\/(?:embed\\/|v\\/|watch\\/|watch\\?v=|watch\\?.+&v=|shorts\\/|live\\/))((\\w|-){11})|youtube\\.com\\/playlist\\?list=|youtube\\.com\\/user\\//;\nconst MATCH_URL_SOUNDCLOUD = /(?:soundcloud\\.com|snd\\.sc)\\/[^.]+$/;\nconst MATCH_URL_VIMEO = /vimeo\\.com\\/(?!progressive_redirect).+/;\nconst MATCH_URL_MUX = /stream\\.mux\\.com\\/(?!\\w+\\.m3u8)(\\w+)/;\nconst MATCH_URL_FACEBOOK = /^https?:\\/\\/(www\\.)?facebook\\.com.*\\/(video(s)?|watch|story)(\\.php?|\\/).+$/;\nconst MATCH_URL_FACEBOOK_WATCH = /^https?:\\/\\/fb\\.watch\\/.+$/;\nconst MATCH_URL_STREAMABLE = /streamable\\.com\\/([a-z0-9]+)$/;\nconst MATCH_URL_WISTIA = /(?:wistia\\.(?:com|net)|wi\\.st)\\/(?:medias|embed)\\/(?:iframe\\/)?([^?]+)/;\nconst MATCH_URL_TWITCH_VIDEO = /(?:www\\.|go\\.)?twitch\\.tv\\/videos\\/(\\d+)($|\\?)/;\nconst MATCH_URL_TWITCH_CHANNEL = /(?:www\\.|go\\.)?twitch\\.tv\\/([a-zA-Z0-9_]+)($|\\?)/;\nconst MATCH_URL_DAILYMOTION = /^(?:(?:https?):)?(?:\\/\\/)?(?:www\\.)?(?:(?:dailymotion\\.com(?:\\/embed)?\\/video)|dai\\.ly)\\/([a-zA-Z0-9]+)(?:_[\\w_-]+)?(?:[\\w.#_-]+)?/;\nconst MATCH_URL_MIXCLOUD = /mixcloud\\.com\\/([^/]+\\/[^/]+)/;\nconst MATCH_URL_VIDYARD = /vidyard.com\\/(?:watch\\/)?([a-zA-Z0-9-_]+)/;\nconst MATCH_URL_KALTURA = /^https?:\\/\\/[a-zA-Z]+\\.kaltura.(com|org)\\/p\\/([0-9]+)\\/sp\\/([0-9]+)00\\/embedIframeJs\\/uiconf_id\\/([0-9]+)\\/partner_id\\/([0-9]+)(.*)entry_id.([a-zA-Z0-9-_].*)$/;\nconst AUDIO_EXTENSIONS = /\\.(m4a|m4b|mp4a|mpga|mp2|mp2a|mp3|m2a|m3a|wav|weba|aac|oga|spx)($|\\?)/i;\nconst VIDEO_EXTENSIONS = /\\.(mp4|og[gv]|webm|mov|m4v)(#t=[,\\d+]+)?($|\\?)/i;\nconst HLS_EXTENSIONS = /\\.(m3u8)($|\\?)/i;\nconst DASH_EXTENSIONS = /\\.(mpd)($|\\?)/i;\nconst FLV_EXTENSIONS = /\\.(flv)($|\\?)/i;\nconst canPlayFile = (url) => {\n  if (url instanceof Array) {\n    for (const item of url) {\n      if (typeof item === \"string\" && canPlayFile(item)) {\n        return true;\n      }\n      if (canPlayFile(item.src)) {\n        return true;\n      }\n    }\n    return false;\n  }\n  if ((0, import_utils.isMediaStream)(url) || (0, import_utils.isBlobUrl)(url)) {\n    return true;\n  }\n  return AUDIO_EXTENSIONS.test(url) || VIDEO_EXTENSIONS.test(url) || HLS_EXTENSIONS.test(url) || DASH_EXTENSIONS.test(url) || FLV_EXTENSIONS.test(url);\n};\nconst canPlay = {\n  youtube: (url) => {\n    if (url instanceof Array) {\n      return url.every((item) => MATCH_URL_YOUTUBE.test(item));\n    }\n    return MATCH_URL_YOUTUBE.test(url);\n  },\n  soundcloud: (url) => MATCH_URL_SOUNDCLOUD.test(url) && !AUDIO_EXTENSIONS.test(url),\n  vimeo: (url) => MATCH_URL_VIMEO.test(url) && !VIDEO_EXTENSIONS.test(url) && !HLS_EXTENSIONS.test(url),\n  mux: (url) => MATCH_URL_MUX.test(url),\n  facebook: (url) => MATCH_URL_FACEBOOK.test(url) || MATCH_URL_FACEBOOK_WATCH.test(url),\n  streamable: (url) => MATCH_URL_STREAMABLE.test(url),\n  wistia: (url) => MATCH_URL_WISTIA.test(url),\n  twitch: (url) => MATCH_URL_TWITCH_VIDEO.test(url) || MATCH_URL_TWITCH_CHANNEL.test(url),\n  dailymotion: (url) => MATCH_URL_DAILYMOTION.test(url),\n  mixcloud: (url) => MATCH_URL_MIXCLOUD.test(url),\n  vidyard: (url) => MATCH_URL_VIDYARD.test(url),\n  kaltura: (url) => MATCH_URL_KALTURA.test(url),\n  file: canPlayFile\n};\n"], "mappings": ";;;;;;;;AAAA;AAAA;AACA,WAAO,UAAU,SAAS,KAAM,KAAK,MAAM,IAAI;AAC7C,UAAI,OAAO,SAAS,QAAQ,SAAS,qBAAqB,MAAM,EAAE,CAAC;AACnE,UAAI,SAAS,SAAS,cAAc,QAAQ;AAE5C,UAAI,OAAO,SAAS,YAAY;AAC9B,aAAK;AACL,eAAO,CAAC;AAAA,MACV;AAEA,aAAO,QAAQ,CAAC;AAChB,WAAK,MAAM,WAAW;AAAA,MAAC;AAEvB,aAAO,OAAO,KAAK,QAAQ;AAC3B,aAAO,UAAU,KAAK,WAAW;AACjC,aAAO,QAAQ,WAAW,OAAO,CAAC,CAAC,KAAK,QAAQ;AAChD,aAAO,MAAM;AAEb,UAAI,KAAK,OAAO;AACd,sBAAc,QAAQ,KAAK,KAAK;AAAA,MAClC;AAEA,UAAI,KAAK,MAAM;AACb,eAAO,OAAO,KAAK,KAAK;AAAA,MAC1B;AAEA,UAAI,QAAQ,YAAY,SAAS,WAAW;AAC5C,YAAM,QAAQ,EAAE;AAKhB,UAAI,CAAC,OAAO,QAAQ;AAClB,iBAAS,QAAQ,EAAE;AAAA,MACrB;AAEA,WAAK,YAAY,MAAM;AAAA,IACzB;AAEA,aAAS,cAAc,QAAQ,OAAO;AACpC,eAAS,QAAQ,OAAO;AACtB,eAAO,aAAa,MAAM,MAAM,IAAI,CAAC;AAAA,MACvC;AAAA,IACF;AAEA,aAAS,SAAU,QAAQ,IAAI;AAC7B,aAAO,SAAS,WAAY;AAC1B,aAAK,UAAU,KAAK,SAAS;AAC7B,WAAG,MAAM,MAAM;AAAA,MACjB;AACA,aAAO,UAAU,WAAY;AAG3B,aAAK,UAAU,KAAK,SAAS;AAC7B,WAAG,IAAI,MAAM,oBAAoB,KAAK,GAAG,GAAG,MAAM;AAAA,MACpD;AAAA,IACF;AAEA,aAAS,QAAS,QAAQ,IAAI;AAC5B,aAAO,qBAAqB,WAAY;AACtC,YAAI,KAAK,cAAc,cAAc,KAAK,cAAc,SAAU;AAClE,aAAK,qBAAqB;AAC1B,WAAG,MAAM,MAAM;AAAA,MACjB;AAAA,IACF;AAAA;AAAA;;;AChEA;AAAA;AAAA;AAEA,QAAI,oBAAoB,SAASA,mBAAkB,OAAO;AACzD,aAAO,gBAAgB,KAAK,KACxB,CAAC,UAAU,KAAK;AAAA,IACrB;AAEA,aAAS,gBAAgB,OAAO;AAC/B,aAAO,CAAC,CAAC,SAAS,OAAO,UAAU;AAAA,IACpC;AAEA,aAAS,UAAU,OAAO;AACzB,UAAI,cAAc,OAAO,UAAU,SAAS,KAAK,KAAK;AAEtD,aAAO,gBAAgB,qBACnB,gBAAgB,mBAChB,eAAe,KAAK;AAAA,IACzB;AAGA,QAAI,eAAe,OAAO,WAAW,cAAc,OAAO;AAC1D,QAAI,qBAAqB,eAAe,OAAO,IAAI,eAAe,IAAI;AAEtE,aAAS,eAAe,OAAO;AAC9B,aAAO,MAAM,aAAa;AAAA,IAC3B;AAEA,aAAS,YAAY,KAAK;AACzB,aAAO,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;AAAA,IACnC;AAEA,aAAS,8BAA8B,OAAO,SAAS;AACtD,aAAQ,QAAQ,UAAU,SAAS,QAAQ,kBAAkB,KAAK,IAC/D,UAAU,YAAY,KAAK,GAAG,OAAO,OAAO,IAC5C;AAAA,IACJ;AAEA,aAAS,kBAAkB,QAAQ,QAAQ,SAAS;AACnD,aAAO,OAAO,OAAO,MAAM,EAAE,IAAI,SAAS,SAAS;AAClD,eAAO,8BAA8B,SAAS,OAAO;AAAA,MACtD,CAAC;AAAA,IACF;AAEA,aAAS,iBAAiB,KAAK,SAAS;AACvC,UAAI,CAAC,QAAQ,aAAa;AACzB,eAAO;AAAA,MACR;AACA,UAAI,cAAc,QAAQ,YAAY,GAAG;AACzC,aAAO,OAAO,gBAAgB,aAAa,cAAc;AAAA,IAC1D;AAEA,aAAS,gCAAgC,QAAQ;AAChD,aAAO,OAAO,wBACX,OAAO,sBAAsB,MAAM,EAAE,OAAO,SAAS,QAAQ;AAC9D,eAAO,OAAO,qBAAqB,KAAK,QAAQ,MAAM;AAAA,MACvD,CAAC,IACC,CAAC;AAAA,IACL;AAEA,aAAS,QAAQ,QAAQ;AACxB,aAAO,OAAO,KAAK,MAAM,EAAE,OAAO,gCAAgC,MAAM,CAAC;AAAA,IAC1E;AAEA,aAAS,mBAAmB,QAAQ,UAAU;AAC7C,UAAI;AACH,eAAO,YAAY;AAAA,MACpB,SAAQ,GAAG;AACV,eAAO;AAAA,MACR;AAAA,IACD;AAGA,aAAS,iBAAiB,QAAQ,KAAK;AACtC,aAAO,mBAAmB,QAAQ,GAAG,KACjC,EAAE,OAAO,eAAe,KAAK,QAAQ,GAAG,KACvC,OAAO,qBAAqB,KAAK,QAAQ,GAAG;AAAA,IAClD;AAEA,aAAS,YAAY,QAAQ,QAAQ,SAAS;AAC7C,UAAI,cAAc,CAAC;AACnB,UAAI,QAAQ,kBAAkB,MAAM,GAAG;AACtC,gBAAQ,MAAM,EAAE,QAAQ,SAAS,KAAK;AACrC,sBAAY,GAAG,IAAI,8BAA8B,OAAO,GAAG,GAAG,OAAO;AAAA,QACtE,CAAC;AAAA,MACF;AACA,cAAQ,MAAM,EAAE,QAAQ,SAAS,KAAK;AACrC,YAAI,iBAAiB,QAAQ,GAAG,GAAG;AAClC;AAAA,QACD;AAEA,YAAI,mBAAmB,QAAQ,GAAG,KAAK,QAAQ,kBAAkB,OAAO,GAAG,CAAC,GAAG;AAC9E,sBAAY,GAAG,IAAI,iBAAiB,KAAK,OAAO,EAAE,OAAO,GAAG,GAAG,OAAO,GAAG,GAAG,OAAO;AAAA,QACpF,OAAO;AACN,sBAAY,GAAG,IAAI,8BAA8B,OAAO,GAAG,GAAG,OAAO;AAAA,QACtE;AAAA,MACD,CAAC;AACD,aAAO;AAAA,IACR;AAEA,aAAS,UAAU,QAAQ,QAAQ,SAAS;AAC3C,gBAAU,WAAW,CAAC;AACtB,cAAQ,aAAa,QAAQ,cAAc;AAC3C,cAAQ,oBAAoB,QAAQ,qBAAqB;AAGzD,cAAQ,gCAAgC;AAExC,UAAI,gBAAgB,MAAM,QAAQ,MAAM;AACxC,UAAI,gBAAgB,MAAM,QAAQ,MAAM;AACxC,UAAI,4BAA4B,kBAAkB;AAElD,UAAI,CAAC,2BAA2B;AAC/B,eAAO,8BAA8B,QAAQ,OAAO;AAAA,MACrD,WAAW,eAAe;AACzB,eAAO,QAAQ,WAAW,QAAQ,QAAQ,OAAO;AAAA,MAClD,OAAO;AACN,eAAO,YAAY,QAAQ,QAAQ,OAAO;AAAA,MAC3C;AAAA,IACD;AAEA,cAAU,MAAM,SAAS,aAAa,OAAO,SAAS;AACrD,UAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AAC1B,cAAM,IAAI,MAAM,mCAAmC;AAAA,MACpD;AAEA,aAAO,MAAM,OAAO,SAAS,MAAM,MAAM;AACxC,eAAO,UAAU,MAAM,MAAM,OAAO;AAAA,MACrC,GAAG,CAAC,CAAC;AAAA,IACN;AAEA,QAAI,cAAc;AAElB,WAAO,UAAU;AAAA;AAAA;;;ACpIjB;AAAA;AAAA,QAAI,WAAW,OAAO;AACtB,QAAI,YAAY,OAAO;AACvB,QAAI,mBAAmB,OAAO;AAC9B,QAAI,oBAAoB,OAAO;AAC/B,QAAI,eAAe,OAAO;AAC1B,QAAI,eAAe,OAAO,UAAU;AACpC,QAAI,WAAW,CAAC,QAAQ,QAAQ;AAC9B,eAAS,QAAQ;AACf,kBAAU,QAAQ,MAAM,EAAE,KAAK,IAAI,IAAI,GAAG,YAAY,KAAK,CAAC;AAAA,IAChE;AACA,QAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,UAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,iBAAS,OAAO,kBAAkB,IAAI;AACpC,cAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,sBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,MACvH;AACA,aAAO;AAAA,IACT;AACA,QAAI,UAAU,CAAC,KAAK,YAAY,YAAY,SAAS,OAAO,OAAO,SAAS,aAAa,GAAG,CAAC,IAAI,CAAC,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA,MAKnG,cAAc,CAAC,OAAO,CAAC,IAAI,aAAa,UAAU,QAAQ,WAAW,EAAE,OAAO,KAAK,YAAY,KAAK,CAAC,IAAI;AAAA,MACzG;AAAA,IACF;AACA,QAAI,eAAe,CAAC,QAAQ,YAAY,UAAU,CAAC,GAAG,cAAc,EAAE,OAAO,KAAK,CAAC,GAAG,GAAG;AACzF,QAAI,gBAAgB,CAAC;AACrB,aAAS,eAAe;AAAA,MACtB,YAAY,MAAM;AAAA,MAClB,WAAW,MAAM;AAAA,MACjB,QAAQ,MAAM;AAAA,MACd,WAAW,MAAM;AAAA,MACjB,eAAe,MAAM;AAAA,MACrB,MAAM,MAAM;AAAA,MACZ,MAAM,MAAM;AAAA,MACZ,cAAc,MAAM;AAAA,MACpB,gBAAgB,MAAM;AAAA,MACtB,aAAa,MAAM;AAAA,MACnB,cAAc,MAAM;AAAA,MACpB,gCAAgC,MAAM;AAAA,IACxC,CAAC;AACD,WAAO,UAAU,aAAa,aAAa;AAC3C,QAAI,eAAe,QAAQ,eAAgB;AAC3C,QAAI,qBAAqB,QAAQ,qBAAsB;AACvD,QAAI,mBAAmB,QAAQ,aAAoB;AACnD,QAAM,OAAO,CAAC,sBAAsB,aAAa,QAAQ,KAAK,YAAY;AACxE,YAAM,MAAM,MAAM,kBAAkB;AACpC,aAAO,OAAO,IAAI,YAAY,aAAa,MAAM,IAAI;AAAA,IACvD,CAAC;AACD,QAAM,oBAAoB;AAC1B,QAAM,kBAAkB;AACxB,QAAM,oBAAoB;AAC1B,QAAM,gBAAgB;AACtB,aAAS,eAAe,KAAK,SAAS;AACpC,UAAI,eAAe,OAAO;AACxB,eAAO;AAAA,MACT;AACA,YAAM,QAAQ,IAAI,MAAM,OAAO;AAC/B,UAAI,OAAO;AACT,cAAM,QAAQ,MAAM,CAAC;AACrB,YAAI,MAAM,MAAM,iBAAiB,GAAG;AAClC,iBAAO,gBAAgB,KAAK;AAAA,QAC9B;AACA,YAAI,cAAc,KAAK,KAAK,GAAG;AAC7B,iBAAO,SAAS,KAAK;AAAA,QACvB;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,aAAS,gBAAgB,OAAO;AAC9B,UAAI,UAAU;AACd,UAAI,QAAQ,kBAAkB,KAAK,KAAK;AACxC,aAAO,UAAU,MAAM;AACrB,cAAM,CAAC,EAAE,OAAO,MAAM,IAAI;AAC1B,YAAI,WAAW;AACb,qBAAW,SAAS,OAAO,EAAE,IAAI,KAAK;AACxC,YAAI,WAAW;AACb,qBAAW,SAAS,OAAO,EAAE,IAAI;AACnC,YAAI,WAAW;AACb,qBAAW,SAAS,OAAO,EAAE;AAC/B,gBAAQ,kBAAkB,KAAK,KAAK;AAAA,MACtC;AACA,aAAO;AAAA,IACT;AACA,aAAS,eAAe,KAAK;AAC3B,aAAO,eAAe,KAAK,iBAAiB;AAAA,IAC9C;AACA,aAAS,aAAa,KAAK;AACzB,aAAO,eAAe,KAAK,eAAe;AAAA,IAC5C;AACA,aAAS,eAAe;AACtB,aAAO,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,OAAO,GAAG,CAAC;AAAA,IAC/C;AACA,aAAS,YAAY,QAAQ;AAC3B,aAAO,OAAO,KAAK,MAAM,EAAE,IAAI,CAAC,QAAQ,GAAG,GAAG,IAAI,OAAO,GAAG,CAAC,EAAE,EAAE,KAAK,GAAG;AAAA,IAC3E;AACA,aAAS,UAAU,KAAK;AACtB,UAAI,OAAO,GAAG,GAAG;AACf,eAAO,OAAO,GAAG;AAAA,MACnB;AACA,UAAI,OAAO,WAAW,OAAO,QAAQ,GAAG,GAAG;AACzC,eAAO,OAAO,QAAQ,GAAG;AAAA,MAC3B;AACA,UAAI,OAAO,UAAU,OAAO,OAAO,WAAW,OAAO,OAAO,QAAQ,GAAG,GAAG;AACxE,eAAO,OAAO,OAAO,QAAQ,GAAG;AAAA,MAClC;AACA,aAAO;AAAA,IACT;AACA,QAAM,WAAW,CAAC;AAClB,QAAM,SAAS,aAAa,SAAS,QAAQ,KAAK,WAAW,WAAW,MAAM,WAAW,MAAM,MAAM,cAAc,mBAAmB,SAAS;AAC7I,YAAM,iBAAiB,UAAU,SAAS;AAC1C,UAAI,kBAAkB,SAAS,cAAc,GAAG;AAC9C,eAAO,QAAQ,QAAQ,cAAc;AAAA,MACvC;AACA,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,YAAI,SAAS,GAAG,GAAG;AACjB,mBAAS,GAAG,EAAE,KAAK,EAAE,SAAS,OAAO,CAAC;AACtC;AAAA,QACF;AACA,iBAAS,GAAG,IAAI,CAAC,EAAE,SAAS,OAAO,CAAC;AACpC,cAAM,WAAW,CAAC,QAAQ;AACxB,mBAAS,GAAG,EAAE,QAAQ,CAAC,YAAY,QAAQ,QAAQ,GAAG,CAAC;AAAA,QACzD;AACA,YAAI,UAAU;AACZ,gBAAM,kBAAkB,OAAO,QAAQ;AACvC,iBAAO,QAAQ,IAAI,WAAW;AAC5B,gBAAI;AACF,8BAAgB;AAClB,qBAAS,UAAU,SAAS,CAAC;AAAA,UAC/B;AAAA,QACF;AACA,oBAAY,KAAK,CAAC,QAAQ;AACxB,cAAI,KAAK;AACP,qBAAS,GAAG,EAAE,QAAQ,CAAC,YAAY,QAAQ,OAAO,GAAG,CAAC;AACtD,qBAAS,GAAG,IAAI;AAAA,UAClB,WAAW,CAAC,UAAU;AACpB,qBAAS,UAAU,SAAS,CAAC;AAAA,UAC/B;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AACD,aAAS,UAAU,OAAO,cAAc;AACtC,cAAQ,GAAG,iBAAiB,SAAS,aAAa,QAAQ,MAAM,MAAM;AAAA,IACxE;AACA,aAAS,KAAK,WAAW,QAAQ;AAC/B,YAAM,WAAW,CAAC,EAAE,OAAO,GAAG,MAAM;AACpC,YAAM,SAAS,CAAC;AAChB,YAAM,OAAO,OAAO,KAAK,MAAM;AAC/B,iBAAW,OAAO,MAAM;AACtB,YAAI,SAAS,QAAQ,GAAG,MAAM,IAAI;AAChC,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAC1B;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,aAAS,WAAW,WAAW,MAAM;AACnC,UAAI,CAAC,KAAK,UAAU,CAAC,KAAK,OAAO,MAAM,GAAG;AACxC,YAAI,UAAU,gBAAgB,KAAK,YAAY,WAAW,4BAA4B,MAAM;AAC5F,YAAI,CAAC,KAAK,QAAQ;AAChB,qBAAW;AAAA,QACb,WAAW,CAAC,KAAK,OAAO,MAAM,GAAG;AAC/B,qBAAW;AAAA,QACb;AACA,gBAAQ,KAAK,SAAS,qBAAqB,EAAE;AAC7C,eAAO;AAAA,MACT;AACA,aAAO,KAAK,OAAO,MAAM,EAAE,GAAG,IAAI;AAAA,IACpC;AACA,aAAS,cAAc,KAAK;AAC1B,aAAO,OAAO,WAAW,eAAe,OAAO,OAAO,gBAAgB,eAAe,eAAe,OAAO;AAAA,IAC7G;AACA,aAAS,UAAU,KAAK;AACtB,aAAO,SAAS,KAAK,GAAG;AAAA,IAC1B;AACA,aAAS,+BAA+B,QAAQ,SAAS,cAAc,OAAO,GAAG;AAC/E,YAAM,YAAY,cAAc,KAAK,UAAU,SAAS,MAAM;AAC9D,aAAO,MAAM,kCAAkC,OAAO,MAAM,8BAA8B,cAAc;AAAA,IAC1G;AACA,aAAS,aAAa,IAAI;AACxB,UAAI,OAAO;AACT,cAAM,OAAO,IAAI,SAAS,KAAK,KAAK,GAAG,IAAI;AAC3C,aAAK,OAAO;AACZ,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AAAA;AAAA;;;AC1LA;AAAA;AAAA,QAAI,YAAY,OAAO;AACvB,QAAI,mBAAmB,OAAO;AAC9B,QAAI,oBAAoB,OAAO;AAC/B,QAAI,eAAe,OAAO,UAAU;AACpC,QAAI,WAAW,CAAC,QAAQ,QAAQ;AAC9B,eAAS,QAAQ;AACf,kBAAU,QAAQ,MAAM,EAAE,KAAK,IAAI,IAAI,GAAG,YAAY,KAAK,CAAC;AAAA,IAChE;AACA,QAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,UAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,iBAAS,OAAO,kBAAkB,IAAI;AACpC,cAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,sBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,MACvH;AACA,aAAO;AAAA,IACT;AACA,QAAI,eAAe,CAAC,QAAQ,YAAY,UAAU,CAAC,GAAG,cAAc,EAAE,OAAO,KAAK,CAAC,GAAG,GAAG;AACzF,QAAI,mBAAmB,CAAC;AACxB,aAAS,kBAAkB;AAAA,MACzB,kBAAkB,MAAM;AAAA,MACxB,iBAAiB,MAAM;AAAA,MACvB,gBAAgB,MAAM;AAAA,MACtB,gBAAgB,MAAM;AAAA,MACtB,uBAAuB,MAAM;AAAA,MAC7B,oBAAoB,MAAM;AAAA,MAC1B,0BAA0B,MAAM;AAAA,MAChC,mBAAmB,MAAM;AAAA,MACzB,oBAAoB,MAAM;AAAA,MAC1B,eAAe,MAAM;AAAA,MACrB,sBAAsB,MAAM;AAAA,MAC5B,sBAAsB,MAAM;AAAA,MAC5B,0BAA0B,MAAM;AAAA,MAChC,wBAAwB,MAAM;AAAA,MAC9B,mBAAmB,MAAM;AAAA,MACzB,iBAAiB,MAAM;AAAA,MACvB,kBAAkB,MAAM;AAAA,MACxB,mBAAmB,MAAM;AAAA,MACzB,kBAAkB,MAAM;AAAA,MACxB,SAAS,MAAM;AAAA,IACjB,CAAC;AACD,WAAO,UAAU,aAAa,gBAAgB;AAC9C,QAAI,eAAe;AACnB,QAAM,oBAAoB;AAC1B,QAAM,uBAAuB;AAC7B,QAAM,kBAAkB;AACxB,QAAM,gBAAgB;AACtB,QAAM,qBAAqB;AAC3B,QAAM,2BAA2B;AACjC,QAAM,uBAAuB;AAC7B,QAAM,mBAAmB;AACzB,QAAM,yBAAyB;AAC/B,QAAM,2BAA2B;AACjC,QAAM,wBAAwB;AAC9B,QAAM,qBAAqB;AAC3B,QAAM,oBAAoB;AAC1B,QAAM,oBAAoB;AAC1B,QAAM,mBAAmB;AACzB,QAAM,mBAAmB;AACzB,QAAM,iBAAiB;AACvB,QAAM,kBAAkB;AACxB,QAAM,iBAAiB;AACvB,QAAM,cAAc,CAAC,QAAQ;AAC3B,UAAI,eAAe,OAAO;AACxB,mBAAW,QAAQ,KAAK;AACtB,cAAI,OAAO,SAAS,YAAY,YAAY,IAAI,GAAG;AACjD,mBAAO;AAAA,UACT;AACA,cAAI,YAAY,KAAK,GAAG,GAAG;AACzB,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO;AAAA,MACT;AACA,WAAK,GAAG,aAAa,eAAe,GAAG,MAAM,GAAG,aAAa,WAAW,GAAG,GAAG;AAC5E,eAAO;AAAA,MACT;AACA,aAAO,iBAAiB,KAAK,GAAG,KAAK,iBAAiB,KAAK,GAAG,KAAK,eAAe,KAAK,GAAG,KAAK,gBAAgB,KAAK,GAAG,KAAK,eAAe,KAAK,GAAG;AAAA,IACrJ;AACA,QAAM,UAAU;AAAA,MACd,SAAS,CAAC,QAAQ;AAChB,YAAI,eAAe,OAAO;AACxB,iBAAO,IAAI,MAAM,CAAC,SAAS,kBAAkB,KAAK,IAAI,CAAC;AAAA,QACzD;AACA,eAAO,kBAAkB,KAAK,GAAG;AAAA,MACnC;AAAA,MACA,YAAY,CAAC,QAAQ,qBAAqB,KAAK,GAAG,KAAK,CAAC,iBAAiB,KAAK,GAAG;AAAA,MACjF,OAAO,CAAC,QAAQ,gBAAgB,KAAK,GAAG,KAAK,CAAC,iBAAiB,KAAK,GAAG,KAAK,CAAC,eAAe,KAAK,GAAG;AAAA,MACpG,KAAK,CAAC,QAAQ,cAAc,KAAK,GAAG;AAAA,MACpC,UAAU,CAAC,QAAQ,mBAAmB,KAAK,GAAG,KAAK,yBAAyB,KAAK,GAAG;AAAA,MACpF,YAAY,CAAC,QAAQ,qBAAqB,KAAK,GAAG;AAAA,MAClD,QAAQ,CAAC,QAAQ,iBAAiB,KAAK,GAAG;AAAA,MAC1C,QAAQ,CAAC,QAAQ,uBAAuB,KAAK,GAAG,KAAK,yBAAyB,KAAK,GAAG;AAAA,MACtF,aAAa,CAAC,QAAQ,sBAAsB,KAAK,GAAG;AAAA,MACpD,UAAU,CAAC,QAAQ,mBAAmB,KAAK,GAAG;AAAA,MAC9C,SAAS,CAAC,QAAQ,kBAAkB,KAAK,GAAG;AAAA,MAC5C,SAAS,CAAC,QAAQ,kBAAkB,KAAK,GAAG;AAAA,MAC5C,MAAM;AAAA,IACR;AAAA;AAAA;", "names": ["isMergeableObject"]}