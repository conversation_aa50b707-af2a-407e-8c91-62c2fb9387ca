// Advanced caching service with multiple storage options
class CacheService {
  constructor() {
    this.memoryCache = new Map();
    this.defaultTTL = 5 * 60 * 1000; // 5 minutes
    this.maxMemoryItems = 100;
    this.compressionEnabled = true;
  }

  // Generate cache key
  generateKey(url, params = {}) {
    const sortedParams = Object.keys(params)
      .sort()
      .reduce((result, key) => {
        result[key] = params[key];
        return result;
      }, {});
    
    return `${url}_${JSON.stringify(sortedParams)}`;
  }

  // Compress data for storage
  compress(data) {
    if (!this.compressionEnabled) return data;
    
    try {
      return JSON.stringify(data);
    } catch (error) {
      console.warn('Compression failed:', error);
      return data;
    }
  }

  // Decompress data from storage
  decompress(data) {
    if (!this.compressionEnabled) return data;
    
    try {
      return typeof data === 'string' ? JSON.parse(data) : data;
    } catch (error) {
      console.warn('Decompression failed:', error);
      return data;
    }
  }

  // Memory cache operations
  setMemoryCache(key, data, ttl = this.defaultTTL) {
    // Remove oldest items if cache is full
    if (this.memoryCache.size >= this.maxMemoryItems) {
      const firstKey = this.memoryCache.keys().next().value;
      this.memoryCache.delete(firstKey);
    }

    this.memoryCache.set(key, {
      data: this.compress(data),
      timestamp: Date.now(),
      ttl
    });
  }

  getMemoryCache(key) {
    const cached = this.memoryCache.get(key);
    if (!cached) return null;

    if (Date.now() - cached.timestamp > cached.ttl) {
      this.memoryCache.delete(key);
      return null;
    }

    return this.decompress(cached.data);
  }

  // LocalStorage operations
  setLocalStorage(key, data, ttl = this.defaultTTL) {
    try {
      const cacheData = {
        data: this.compress(data),
        timestamp: Date.now(),
        ttl
      };
      localStorage.setItem(`cache_${key}`, JSON.stringify(cacheData));
    } catch (error) {
      console.warn('LocalStorage cache write failed:', error);
    }
  }

  getLocalStorage(key) {
    try {
      const cached = localStorage.getItem(`cache_${key}`);
      if (!cached) return null;

      const { data, timestamp, ttl } = JSON.parse(cached);
      
      if (Date.now() - timestamp > ttl) {
        localStorage.removeItem(`cache_${key}`);
        return null;
      }

      return this.decompress(data);
    } catch (error) {
      console.warn('LocalStorage cache read failed:', error);
      return null;
    }
  }

  // SessionStorage operations
  setSessionStorage(key, data, ttl = this.defaultTTL) {
    try {
      const cacheData = {
        data: this.compress(data),
        timestamp: Date.now(),
        ttl
      };
      sessionStorage.setItem(`cache_${key}`, JSON.stringify(cacheData));
    } catch (error) {
      console.warn('SessionStorage cache write failed:', error);
    }
  }

  getSessionStorage(key) {
    try {
      const cached = sessionStorage.getItem(`cache_${key}`);
      if (!cached) return null;

      const { data, timestamp, ttl } = JSON.parse(cached);
      
      if (Date.now() - timestamp > ttl) {
        sessionStorage.removeItem(`cache_${key}`);
        return null;
      }

      return this.decompress(data);
    } catch (error) {
      console.warn('SessionStorage cache read failed:', error);
      return null;
    }
  }

  // IndexedDB operations (for large data)
  async setIndexedDB(key, data, ttl = this.defaultTTL) {
    try {
      const db = await this.openIndexedDB();
      const transaction = db.transaction(['cache'], 'readwrite');
      const store = transaction.objectStore('cache');
      
      await store.put({
        key,
        data: this.compress(data),
        timestamp: Date.now(),
        ttl
      });
    } catch (error) {
      console.warn('IndexedDB cache write failed:', error);
    }
  }

  async getIndexedDB(key) {
    try {
      const db = await this.openIndexedDB();
      const transaction = db.transaction(['cache'], 'readonly');
      const store = transaction.objectStore('cache');
      const result = await store.get(key);

      if (!result) return null;

      if (Date.now() - result.timestamp > result.ttl) {
        await this.deleteIndexedDB(key);
        return null;
      }

      return this.decompress(result.data);
    } catch (error) {
      console.warn('IndexedDB cache read failed:', error);
      return null;
    }
  }

  async deleteIndexedDB(key) {
    try {
      const db = await this.openIndexedDB();
      const transaction = db.transaction(['cache'], 'readwrite');
      const store = transaction.objectStore('cache');
      await store.delete(key);
    } catch (error) {
      console.warn('IndexedDB cache delete failed:', error);
    }
  }

  openIndexedDB() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('LMSCache', 1);
      
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
      
      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        if (!db.objectStoreNames.contains('cache')) {
          db.createObjectStore('cache', { keyPath: 'key' });
        }
      };
    });
  }

  // Main cache methods with fallback strategy
  async set(key, data, options = {}) {
    const {
      ttl = this.defaultTTL,
      storage = 'auto', // 'memory', 'localStorage', 'sessionStorage', 'indexedDB', 'auto'
      priority = 'normal' // 'low', 'normal', 'high'
    } = options;

    const cacheKey = typeof key === 'string' ? key : this.generateKey(key.url, key.params);

    // Determine storage strategy
    let storageType = storage;
    if (storage === 'auto') {
      const dataSize = JSON.stringify(data).length;
      if (dataSize > 1024 * 1024) { // > 1MB
        storageType = 'indexedDB';
      } else if (dataSize > 100 * 1024) { // > 100KB
        storageType = 'localStorage';
      } else if (priority === 'high') {
        storageType = 'memory';
      } else {
        storageType = 'sessionStorage';
      }
    }

    // Store in selected storage
    switch (storageType) {
      case 'memory':
        this.setMemoryCache(cacheKey, data, ttl);
        break;
      case 'localStorage':
        this.setLocalStorage(cacheKey, data, ttl);
        break;
      case 'sessionStorage':
        this.setSessionStorage(cacheKey, data, ttl);
        break;
      case 'indexedDB':
        await this.setIndexedDB(cacheKey, data, ttl);
        break;
    }

    // Also store in memory for fast access if not already there
    if (storageType !== 'memory' && priority === 'high') {
      this.setMemoryCache(cacheKey, data, ttl);
    }
  }

  async get(key, options = {}) {
    const { fallbackToStorage = true } = options;
    const cacheKey = typeof key === 'string' ? key : this.generateKey(key.url, key.params);

    // Try memory cache first (fastest)
    let data = this.getMemoryCache(cacheKey);
    if (data) return data;

    if (!fallbackToStorage) return null;

    // Try session storage
    data = this.getSessionStorage(cacheKey);
    if (data) {
      // Promote to memory cache
      this.setMemoryCache(cacheKey, data);
      return data;
    }

    // Try local storage
    data = this.getLocalStorage(cacheKey);
    if (data) {
      // Promote to memory cache
      this.setMemoryCache(cacheKey, data);
      return data;
    }

    // Try IndexedDB
    data = await this.getIndexedDB(cacheKey);
    if (data) {
      // Promote to memory cache
      this.setMemoryCache(cacheKey, data);
      return data;
    }

    return null;
  }

  // Clear cache methods
  clear(pattern) {
    // Clear memory cache
    if (pattern) {
      for (const key of this.memoryCache.keys()) {
        if (key.includes(pattern)) {
          this.memoryCache.delete(key);
        }
      }
    } else {
      this.memoryCache.clear();
    }

    // Clear localStorage
    for (let i = localStorage.length - 1; i >= 0; i--) {
      const key = localStorage.key(i);
      if (key && key.startsWith('cache_') && (!pattern || key.includes(pattern))) {
        localStorage.removeItem(key);
      }
    }

    // Clear sessionStorage
    for (let i = sessionStorage.length - 1; i >= 0; i--) {
      const key = sessionStorage.key(i);
      if (key && key.startsWith('cache_') && (!pattern || key.includes(pattern))) {
        sessionStorage.removeItem(key);
      }
    }
  }

  // Cache statistics
  getStats() {
    return {
      memoryItems: this.memoryCache.size,
      localStorageItems: Object.keys(localStorage).filter(k => k.startsWith('cache_')).length,
      sessionStorageItems: Object.keys(sessionStorage).filter(k => k.startsWith('cache_')).length
    };
  }

  // Preload data
  async preload(requests) {
    const promises = requests.map(async ({ key, fetcher, options = {} }) => {
      const cached = await this.get(key);
      if (!cached) {
        try {
          const data = await fetcher();
          await this.set(key, data, options);
          return { key, success: true, data };
        } catch (error) {
          return { key, success: false, error };
        }
      }
      return { key, success: true, cached: true };
    });

    return Promise.allSettled(promises);
  }
}

// Create singleton instance
const cacheService = new CacheService();

export default cacheService;
