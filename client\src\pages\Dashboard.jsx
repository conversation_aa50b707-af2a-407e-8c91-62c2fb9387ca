import React, { useState } from 'react';
import {
  Container,
  Typo<PERSON>,
  Box,
  Grid,
  Card,
  CardContent,
  Avatar,
  Chip,
  Button,
  LinearProgress,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  IconButton,
  Tooltip,
  Alert
} from '@mui/material';
import {
  School as SchoolIcon,
  TrendingUp as TrendingUpIcon,
  Assignment as AssignmentIcon,
  People as PeopleIcon,
  Add as AddIcon,
  PlayArrow as PlayIcon,
  Quiz as QuizIcon,
  Notifications as NotificationsIcon,
  CalendarToday as CalendarIcon,
  Star as StarIcon
} from '@mui/icons-material';
import { Link } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

const Dashboard = () => {
  const { user } = useAuth();

  // Sample data - in a real app, this would come from API
  const dashboardData = {
    student: {
      enrolledCourses: 5,
      completedCourses: 2,
      inProgressCourses: 3,
      totalTimeSpent: 45, // hours
      recentCourses: [
        {
          id: 1,
          title: "React Development Masterclass",
          progress: 75,
          instructor: "<PERSON>",
          thumbnail: "https://via.placeholder.com/100x60"
        },
        {
          id: 2,
          title: "UI/UX Design Fundamentals",
          progress: 30,
          instructor: "<PERSON>",
          thumbnail: "https://via.placeholder.com/100x60"
        }
      ]
    },
    instructor: {
      totalCourses: 8,
      publishedCourses: 6,
      totalStudents: 1250,
      totalRevenue: 15750,
      recentCourses: [
        {
          id: 1,
          title: "Advanced JavaScript Concepts",
          students: 450,
          rating: 4.8,
          revenue: 4500
        },
        {
          id: 2,
          title: "Node.js Backend Development",
          students: 320,
          rating: 4.9,
          revenue: 3200
        }
      ]
    }
  };

  const data = dashboardData[user?.role] || dashboardData.student;

  const StatCard = ({ icon, title, value, color = 'primary' }) => (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box display="flex" alignItems="center" mb={2}>
          <Avatar sx={{ bgcolor: `${color}.main`, mr: 2 }}>
            {icon}
          </Avatar>
          <Typography variant="h6" component="div">
            {title}
          </Typography>
        </Box>
        <Typography variant="h4" component="div" fontWeight="bold">
          {value}
        </Typography>
      </CardContent>
    </Card>
  );

  const renderStudentDashboard = () => (
    <>
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            icon={<SchoolIcon />}
            title="Enrolled Courses"
            value={data.enrolledCourses}
            color="primary"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            icon={<TrendingUpIcon />}
            title="Completed"
            value={data.completedCourses}
            color="success"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            icon={<AssignmentIcon />}
            title="In Progress"
            value={data.inProgressCourses}
            color="warning"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            icon={<TrendingUpIcon />}
            title="Hours Learned"
            value={`${data.totalTimeSpent}h`}
            color="info"
          />
        </Grid>
      </Grid>

      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Continue Learning
          </Typography>
          {data.recentCourses.map((course) => (
            <Box key={course.id} sx={{ mb: 3, p: 2, border: '1px solid', borderColor: 'grey.200', borderRadius: 1 }}>
              <Box display="flex" alignItems="center" mb={2}>
                <Box
                  component="img"
                  src={course.thumbnail}
                  alt={course.title}
                  sx={{ width: 100, height: 60, borderRadius: 1, mr: 2 }}
                />
                <Box flexGrow={1}>
                  <Typography variant="subtitle1" fontWeight="bold">
                    {course.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    by {course.instructor}
                  </Typography>
                </Box>
                <Button variant="outlined" size="small">
                  Continue
                </Button>
              </Box>
              <Box>
                <Box display="flex" justifyContent="space-between" mb={1}>
                  <Typography variant="body2">Progress</Typography>
                  <Typography variant="body2">{course.progress}%</Typography>
                </Box>
                <LinearProgress variant="determinate" value={course.progress} />
              </Box>
            </Box>
          ))}
        </CardContent>
      </Card>
    </>
  );

  const renderInstructorDashboard = () => (
    <>
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            icon={<SchoolIcon />}
            title="Total Courses"
            value={data.totalCourses}
            color="primary"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            icon={<TrendingUpIcon />}
            title="Published"
            value={data.publishedCourses}
            color="success"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            icon={<PeopleIcon />}
            title="Total Students"
            value={data.totalStudents}
            color="info"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            icon={<TrendingUpIcon />}
            title="Revenue"
            value={`$${data.totalRevenue}`}
            color="warning"
          />
        </Grid>
      </Grid>

      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Your Courses Performance
          </Typography>
          {data.recentCourses.map((course) => (
            <Box key={course.id} sx={{ mb: 3, p: 2, border: '1px solid', borderColor: 'grey.200', borderRadius: 1 }}>
              <Box display="flex" justifyContent="space-between" alignItems="center">
                <Box>
                  <Typography variant="subtitle1" fontWeight="bold">
                    {course.title}
                  </Typography>
                  <Box display="flex" gap={1} mt={1}>
                    <Chip label={`${course.students} students`} size="small" />
                    <Chip label={`${course.rating} ⭐`} size="small" color="primary" />
                    <Chip label={`$${course.revenue} revenue`} size="small" color="success" />
                  </Box>
                </Box>
                <Button variant="outlined" size="small">
                  Manage
                </Button>
              </Box>
            </Box>
          ))}
        </CardContent>
      </Card>
    </>
  );

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Box mb={4}>
        <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
          Welcome back, {user?.firstName}!
        </Typography>
        <Typography variant="h6" color="text.secondary">
          {user?.role === 'student' 
            ? "Continue your learning journey" 
            : "Manage your courses and track your impact"
          }
        </Typography>
      </Box>

      {user?.role === 'instructor' ? renderInstructorDashboard() : renderStudentDashboard()}

      {/* Quick Actions */}
      <Grid container spacing={3} sx={{ mt: 4 }}>
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Quick Actions
            </Typography>
            <Grid container spacing={2}>
              {user?.role === 'instructor' ? (
                <>
                  <Grid item xs={12} sm={6} md={3}>
                    <Button
                      component={Link}
                      to="/instructor/create-course"
                      variant="outlined"
                      fullWidth
                      startIcon={<AddIcon />}
                      sx={{ py: 2 }}
                    >
                      Create Course
                    </Button>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Button
                      variant="outlined"
                      fullWidth
                      startIcon={<QuizIcon />}
                      sx={{ py: 2 }}
                    >
                      Create Assessment
                    </Button>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Button
                      variant="outlined"
                      fullWidth
                      startIcon={<PeopleIcon />}
                      sx={{ py: 2 }}
                    >
                      Manage Students
                    </Button>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Button
                      variant="outlined"
                      fullWidth
                      startIcon={<TrendingUpIcon />}
                      sx={{ py: 2 }}
                    >
                      View Analytics
                    </Button>
                  </Grid>
                </>
              ) : (
                <>
                  <Grid item xs={12} sm={6} md={3}>
                    <Button
                      component={Link}
                      to="/courses"
                      variant="outlined"
                      fullWidth
                      startIcon={<SchoolIcon />}
                      sx={{ py: 2 }}
                    >
                      Browse Courses
                    </Button>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Button
                      variant="outlined"
                      fullWidth
                      startIcon={<PlayIcon />}
                      sx={{ py: 2 }}
                    >
                      Continue Learning
                    </Button>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Button
                      variant="outlined"
                      fullWidth
                      startIcon={<QuizIcon />}
                      sx={{ py: 2 }}
                    >
                      Take Assessment
                    </Button>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Button
                      component={Link}
                      to="/profile"
                      variant="outlined"
                      fullWidth
                      startIcon={<PeopleIcon />}
                      sx={{ py: 2 }}
                    >
                      Update Profile
                    </Button>
                  </Grid>
                </>
              )}
            </Grid>
          </Paper>
        </Grid>
      </Grid>

      {/* Recent Activity */}
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Recent Activity
            </Typography>
            <List>
              <ListItem>
                <ListItemIcon>
                  <PlayIcon color="primary" />
                </ListItemIcon>
                <ListItemText
                  primary="Completed lesson: JavaScript Basics"
                  secondary="2 hours ago"
                />
              </ListItem>
              <Divider />
              <ListItem>
                <ListItemIcon>
                  <QuizIcon color="success" />
                </ListItemIcon>
                <ListItemText
                  primary="Passed HTML & CSS Quiz with 95%"
                  secondary="1 day ago"
                />
              </ListItem>
              <Divider />
              <ListItem>
                <ListItemIcon>
                  <SchoolIcon color="info" />
                </ListItemIcon>
                <ListItemText
                  primary="Enrolled in React Development Course"
                  secondary="3 days ago"
                />
              </ListItem>
            </List>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Upcoming Deadlines
            </Typography>
            <List>
              <ListItem>
                <ListItemIcon>
                  <CalendarIcon color="warning" />
                </ListItemIcon>
                <ListItemText
                  primary="JavaScript Assignment Due"
                  secondary="Tomorrow, 11:59 PM"
                />
              </ListItem>
              <Divider />
              <ListItem>
                <ListItemIcon>
                  <QuizIcon color="error" />
                </ListItemIcon>
                <ListItemText
                  primary="React Final Exam"
                  secondary="In 3 days"
                />
              </ListItem>
              <Divider />
              <ListItem>
                <ListItemIcon>
                  <AssignmentIcon color="info" />
                </ListItemIcon>
                <ListItemText
                  primary="Portfolio Project Submission"
                  secondary="Next week"
                />
              </ListItem>
            </List>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default Dashboard;
