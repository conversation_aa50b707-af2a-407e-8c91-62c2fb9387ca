const mongoose = require('mongoose');

const messageSchema = new mongoose.Schema({
  content: {
    type: String,
    required: [true, 'Message content is required'],
    trim: true,
    maxlength: [1000, 'Message cannot exceed 1000 characters']
  },
  sender: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  messageType: {
    type: String,
    enum: ['text', 'image', 'file', 'system'],
    default: 'text'
  },
  attachments: [{
    filename: String,
    url: String,
    fileType: String,
    fileSize: Number
  }],
  isEdited: {
    type: Boolean,
    default: false
  },
  editedAt: {
    type: Date
  },
  reactions: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    emoji: {
      type: String,
      required: true
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],
  replyTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Message'
  }
}, {
  timestamps: true
});

const chatRoomSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Chat room name is required'],
    trim: true,
    maxlength: [100, 'Chat room name cannot exceed 100 characters']
  },
  description: {
    type: String,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  course: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Course',
    required: true
  },
  roomType: {
    type: String,
    enum: ['general', 'qa', 'announcements', 'study-group'],
    default: 'general'
  },
  participants: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    role: {
      type: String,
      enum: ['member', 'moderator', 'admin'],
      default: 'member'
    },
    joinedAt: {
      type: Date,
      default: Date.now
    },
    lastSeen: {
      type: Date,
      default: Date.now
    }
  }],
  messages: [messageSchema],
  isActive: {
    type: Boolean,
    default: true
  },
  settings: {
    allowFileSharing: {
      type: Boolean,
      default: true
    },
    allowReactions: {
      type: Boolean,
      default: true
    },
    moderationEnabled: {
      type: Boolean,
      default: false
    }
  },
  pinnedMessages: [{
    type: mongoose.Schema.Types.ObjectId
  }]
}, {
  timestamps: true
});

// Indexes for better performance
chatRoomSchema.index({ course: 1 });
chatRoomSchema.index({ 'participants.user': 1 });
chatRoomSchema.index({ 'messages.createdAt': -1 });

// Virtual for message count
chatRoomSchema.virtual('messageCount').get(function() {
  return this.messages.length;
});

// Virtual for active participants count
chatRoomSchema.virtual('activeParticipantsCount').get(function() {
  return this.participants.length;
});

// Method to add participant
chatRoomSchema.methods.addParticipant = function(userId, role = 'member') {
  const existingParticipant = this.participants.find(
    p => p.user.toString() === userId.toString()
  );
  
  if (!existingParticipant) {
    this.participants.push({
      user: userId,
      role: role
    });
  }
  
  return this.save();
};

// Method to remove participant
chatRoomSchema.methods.removeParticipant = function(userId) {
  this.participants = this.participants.filter(
    p => p.user.toString() !== userId.toString()
  );
  
  return this.save();
};

// Method to add message
chatRoomSchema.methods.addMessage = function(messageData) {
  this.messages.push(messageData);
  
  // Update last seen for sender
  const participant = this.participants.find(
    p => p.user.toString() === messageData.sender.toString()
  );
  
  if (participant) {
    participant.lastSeen = new Date();
  }
  
  return this.save();
};

// Method to get recent messages
chatRoomSchema.methods.getRecentMessages = function(limit = 50) {
  return this.messages
    .sort({ createdAt: -1 })
    .limit(limit)
    .populate('sender', 'firstName lastName avatar')
    .populate('replyTo');
};

// Ensure virtual fields are serialized
chatRoomSchema.set('toJSON', {
  virtuals: true
});

module.exports = mongoose.model('ChatRoom', chatRoomSchema);
