import axios from 'axios';

// API base URL
const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

// Create axios instance
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (email, password) => api.post('/auth/login', { email, password }),
  register: (userData) => api.post('/auth/register', userData),
  getProfile: () => api.get('/auth/me'),
  updateProfile: (profileData) => api.put('/auth/profile', profileData),
};

// Courses API
export const coursesAPI = {
  getAllCourses: (params = {}) => api.get('/courses', { params }),
  getCourse: (id) => api.get(`/courses/${id}`),
  createCourse: (courseData) => api.post('/courses', courseData),
  updateCourse: (id, courseData) => api.put(`/courses/${id}`, courseData),
  deleteCourse: (id) => api.delete(`/courses/${id}`),
  enrollInCourse: (id) => api.post(`/courses/${id}/enroll`),
  addLesson: (courseId, lessonData) => api.post(`/courses/${courseId}/lessons`, lessonData),
  addReview: (courseId, reviewData) => api.post(`/courses/${courseId}/reviews`, reviewData),
};

// Users API
export const usersAPI = {
  getAllUsers: (params = {}) => api.get('/users', { params }),
  getUser: (id) => api.get(`/users/${id}`),
  updateUser: (id, userData) => api.put(`/users/${id}`, userData),
  deleteUser: (id) => api.delete(`/users/${id}`),
  uploadAvatar: (formData) => api.post('/users/upload-avatar', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),
  getDashboard: (id) => api.get(`/users/${id}/dashboard`),
};

// Assessments API
export const assessmentsAPI = {
  getCourseAssessments: (courseId) => api.get(`/assessments/course/${courseId}`),
  getAssessment: (id) => api.get(`/assessments/${id}`),
  createAssessment: (assessmentData) => api.post('/assessments', assessmentData),
  updateAssessment: (id, assessmentData) => api.put(`/assessments/${id}`, assessmentData),
  deleteAssessment: (id) => api.delete(`/assessments/${id}`),
  submitAssessment: (id, submissionData) => api.post(`/assessments/${id}/submit`, submissionData),
  getSubmissions: (id) => api.get(`/assessments/${id}/submissions`),
};

// Progress API
export const progressAPI = {
  getCourseProgress: (courseId) => api.get(`/progress/course/${courseId}`),
  updateLessonProgress: (courseId, lessonId, progressData) => 
    api.put(`/progress/course/${courseId}/lesson/${lessonId}`, progressData),
  addNote: (courseId, noteData) => api.post(`/progress/course/${courseId}/notes`, noteData),
  addBookmark: (courseId, bookmarkData) => api.post(`/progress/course/${courseId}/bookmarks`, bookmarkData),
};

// File upload helper
export const uploadFile = async (file, type = 'image') => {
  const formData = new FormData();
  formData.append(type, file);
  
  try {
    const response = await api.post(`/upload/${type}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};

// Generic API helper functions
export const apiHelpers = {
  // Handle API errors
  handleError: (error) => {
    if (error.response) {
      // Server responded with error status
      return error.response.data.message || 'An error occurred';
    } else if (error.request) {
      // Request was made but no response received
      return 'Network error. Please check your connection.';
    } else {
      // Something else happened
      return error.message || 'An unexpected error occurred';
    }
  },

  // Format query parameters
  formatParams: (params) => {
    const formatted = {};
    Object.keys(params).forEach(key => {
      if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
        formatted[key] = params[key];
      }
    });
    return formatted;
  },

  // Debounce function for search
  debounce: (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },
};

export default api;
