import axios from 'axios';
import cacheService from './cacheService';

// API base URL
const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

// Create axios instance
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (email, password) => api.post('/auth/login', { email, password }),
  register: (userData) => api.post('/auth/register', userData),
  getProfile: () => api.get('/auth/me'),
  updateProfile: (profileData) => api.put('/auth/profile', profileData),
};

// Courses API
export const coursesAPI = {
  getAllCourses: (params = {}) => api.get('/courses', { params }),
  getCourse: (id) => api.get(`/courses/${id}`),
  createCourse: (courseData) => api.post('/courses', courseData),
  updateCourse: (id, courseData) => api.put(`/courses/${id}`, courseData),
  deleteCourse: (id) => api.delete(`/courses/${id}`),
  enrollInCourse: (id) => api.post(`/courses/${id}/enroll`),
  addLesson: (courseId, lessonData) => api.post(`/courses/${courseId}/lessons`, lessonData),
  addReview: (courseId, reviewData) => api.post(`/courses/${courseId}/reviews`, reviewData),
};

// Users API
export const usersAPI = {
  getAllUsers: (params = {}) => api.get('/users', { params }),
  getUser: (id) => api.get(`/users/${id}`),
  updateUser: (id, userData) => api.put(`/users/${id}`, userData),
  deleteUser: (id) => api.delete(`/users/${id}`),
  uploadAvatar: (formData) => api.post('/users/upload-avatar', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),
  getDashboard: (id) => api.get(`/users/${id}/dashboard`),
};

// Assessments API
export const assessmentsAPI = {
  getCourseAssessments: (courseId) => api.get(`/assessments/course/${courseId}`),
  getAssessment: (id) => api.get(`/assessments/${id}`),
  createAssessment: (assessmentData) => api.post('/assessments', assessmentData),
  updateAssessment: (id, assessmentData) => api.put(`/assessments/${id}`, assessmentData),
  deleteAssessment: (id) => api.delete(`/assessments/${id}`),
  submitAssessment: (id, submissionData) => api.post(`/assessments/${id}/submit`, submissionData),
  getSubmissions: (id) => api.get(`/assessments/${id}/submissions`),
};

// Progress API
export const progressAPI = {
  getCourseProgress: (courseId) => api.get(`/progress/course/${courseId}`),
  updateLessonProgress: (courseId, lessonId, progressData) => 
    api.put(`/progress/course/${courseId}/lesson/${lessonId}`, progressData),
  addNote: (courseId, noteData) => api.post(`/progress/course/${courseId}/notes`, noteData),
  addBookmark: (courseId, bookmarkData) => api.post(`/progress/course/${courseId}/bookmarks`, bookmarkData),
};

// File upload helper
export const uploadFile = async (file, type = 'image') => {
  const formData = new FormData();
  formData.append(type, file);
  
  try {
    const response = await api.post(`/upload/${type}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};

// Generic API helper functions
export const apiHelpers = {
  // Handle API errors
  handleError: (error) => {
    if (error.response) {
      // Server responded with error status
      return error.response.data.message || 'An error occurred';
    } else if (error.request) {
      // Request was made but no response received
      return 'Network error. Please check your connection.';
    } else {
      // Something else happened
      return error.message || 'An unexpected error occurred';
    }
  },

  // Format query parameters
  formatParams: (params) => {
    const formatted = {};
    Object.keys(params).forEach(key => {
      if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
        formatted[key] = params[key];
      }
    });
    return formatted;
  },

  // Debounce function for search
  debounce: (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },
};

// Enhanced API methods with caching
export const enhancedAPI = {
  // Cached GET request
  async getCached(url, params = {}, options = {}) {
    const {
      ttl = 5 * 60 * 1000, // 5 minutes default
      forceRefresh = false,
      cacheKey = null
    } = options;

    const key = cacheKey || { url, params };

    if (!forceRefresh) {
      const cached = await cacheService.get(key);
      if (cached) {
        return { data: cached, fromCache: true };
      }
    }

    try {
      const response = await api.get(url, { params });
      await cacheService.set(key, response.data, { ttl });
      return { data: response.data, fromCache: false };
    } catch (error) {
      // Try to return stale cache on error
      const staleCache = await cacheService.get(key, { fallbackToStorage: true });
      if (staleCache) {
        console.warn('API error, returning stale cache:', error);
        return { data: staleCache, fromCache: true, stale: true };
      }
      throw error;
    }
  },

  // Batch requests
  async batchRequests(requests) {
    const promises = requests.map(async (request) => {
      try {
        const response = await api(request);
        return { success: true, data: response.data, request };
      } catch (error) {
        return { success: false, error, request };
      }
    });

    return Promise.allSettled(promises);
  },

  // Optimistic updates
  async optimisticUpdate(url, data, options = {}) {
    const { cacheKey, rollbackData } = options;

    // Update cache immediately
    if (cacheKey) {
      await cacheService.set(cacheKey, data);
    }

    try {
      const response = await api.put(url, data);
      // Update cache with server response
      if (cacheKey) {
        await cacheService.set(cacheKey, response.data);
      }
      return response;
    } catch (error) {
      // Rollback cache on error
      if (cacheKey && rollbackData) {
        await cacheService.set(cacheKey, rollbackData);
      }
      throw error;
    }
  },

  // Prefetch data
  async prefetch(requests) {
    return cacheService.preload(requests);
  },

  // Clear cache
  clearCache(pattern) {
    cacheService.clear(pattern);
  }
};

// Performance monitoring
const performanceMonitor = {
  requests: new Map(),

  startRequest(url) {
    this.requests.set(url, {
      startTime: performance.now(),
      url
    });
  },

  endRequest(url, success = true) {
    const request = this.requests.get(url);
    if (request) {
      const duration = performance.now() - request.startTime;
      console.log(`API Request: ${url} - ${duration.toFixed(2)}ms - ${success ? 'Success' : 'Failed'}`);
      this.requests.delete(url);
    }
  },

  getStats() {
    return {
      activeRequests: this.requests.size,
      requests: Array.from(this.requests.values())
    };
  }
};

// Add performance monitoring to axios
api.interceptors.request.use(
  (config) => {
    performanceMonitor.startRequest(config.url);
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

api.interceptors.response.use(
  (response) => {
    performanceMonitor.endRequest(response.config.url, true);
    return response;
  },
  (error) => {
    performanceMonitor.endRequest(error.config?.url, false);
    return Promise.reject(error);
  }
);

export { performanceMonitor };
export default api;
