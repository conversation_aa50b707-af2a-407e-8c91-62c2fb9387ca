{"version": 3, "sources": ["../../react-player/lib/players/SoundCloud.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar SoundCloud_exports = {};\n__export(SoundCloud_exports, {\n  default: () => SoundCloud\n});\nmodule.exports = __toCommonJS(SoundCloud_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_utils = require(\"../utils\");\nvar import_patterns = require(\"../patterns\");\nconst SDK_URL = \"https://w.soundcloud.com/player/api.js\";\nconst SDK_GLOBAL = \"SC\";\nclass SoundCloud extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    __publicField(this, \"callPlayer\", import_utils.callPlayer);\n    __publicField(this, \"duration\", null);\n    __publicField(this, \"currentTime\", null);\n    __publicField(this, \"fractionLoaded\", null);\n    __publicField(this, \"mute\", () => {\n      this.setVolume(0);\n    });\n    __publicField(this, \"unmute\", () => {\n      if (this.props.volume !== null) {\n        this.setVolume(this.props.volume);\n      }\n    });\n    __publicField(this, \"ref\", (iframe) => {\n      this.iframe = iframe;\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n  }\n  load(url, isReady) {\n    (0, import_utils.getSDK)(SDK_URL, SDK_GLOBAL).then((SC) => {\n      if (!this.iframe)\n        return;\n      const { PLAY, PLAY_PROGRESS, PAUSE, FINISH, ERROR } = SC.Widget.Events;\n      if (!isReady) {\n        this.player = SC.Widget(this.iframe);\n        this.player.bind(PLAY, this.props.onPlay);\n        this.player.bind(PAUSE, () => {\n          const remaining = this.duration - this.currentTime;\n          if (remaining < 0.05) {\n            return;\n          }\n          this.props.onPause();\n        });\n        this.player.bind(PLAY_PROGRESS, (e) => {\n          this.currentTime = e.currentPosition / 1e3;\n          this.fractionLoaded = e.loadedProgress;\n        });\n        this.player.bind(FINISH, () => this.props.onEnded());\n        this.player.bind(ERROR, (e) => this.props.onError(e));\n      }\n      this.player.load(url, {\n        ...this.props.config.options,\n        callback: () => {\n          this.player.getDuration((duration) => {\n            this.duration = duration / 1e3;\n            this.props.onReady();\n          });\n        }\n      });\n    });\n  }\n  play() {\n    this.callPlayer(\"play\");\n  }\n  pause() {\n    this.callPlayer(\"pause\");\n  }\n  stop() {\n  }\n  seekTo(seconds, keepPlaying = true) {\n    this.callPlayer(\"seekTo\", seconds * 1e3);\n    if (!keepPlaying) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n    this.callPlayer(\"setVolume\", fraction * 100);\n  }\n  getDuration() {\n    return this.duration;\n  }\n  getCurrentTime() {\n    return this.currentTime;\n  }\n  getSecondsLoaded() {\n    return this.fractionLoaded * this.duration;\n  }\n  render() {\n    const { display } = this.props;\n    const style = {\n      width: \"100%\",\n      height: \"100%\",\n      display\n    };\n    return /* @__PURE__ */ import_react.default.createElement(\n      \"iframe\",\n      {\n        ref: this.ref,\n        src: `https://w.soundcloud.com/player/?url=${encodeURIComponent(this.props.url)}`,\n        style,\n        frameBorder: 0,\n        allow: \"autoplay\"\n      }\n    );\n  }\n}\n__publicField(SoundCloud, \"displayName\", \"SoundCloud\");\n__publicField(SoundCloud, \"canPlay\", import_patterns.canPlay.soundcloud);\n__publicField(SoundCloud, \"loopOnEnded\", true);\n"], "mappings": ";;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,WAAW,OAAO;AACtB,QAAI,YAAY,OAAO;AACvB,QAAI,mBAAmB,OAAO;AAC9B,QAAI,oBAAoB,OAAO;AAC/B,QAAI,eAAe,OAAO;AAC1B,QAAI,eAAe,OAAO,UAAU;AACpC,QAAI,kBAAkB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,UAAU,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC1J,QAAI,WAAW,CAAC,QAAQ,QAAQ;AAC9B,eAAS,QAAQ;AACf,kBAAU,QAAQ,MAAM,EAAE,KAAK,IAAI,IAAI,GAAG,YAAY,KAAK,CAAC;AAAA,IAChE;AACA,QAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,UAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,iBAAS,OAAO,kBAAkB,IAAI;AACpC,cAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,sBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,MACvH;AACA,aAAO;AAAA,IACT;AACA,QAAI,UAAU,CAAC,KAAK,YAAY,YAAY,SAAS,OAAO,OAAO,SAAS,aAAa,GAAG,CAAC,IAAI,CAAC,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA,MAKnG,cAAc,CAAC,OAAO,CAAC,IAAI,aAAa,UAAU,QAAQ,WAAW,EAAE,OAAO,KAAK,YAAY,KAAK,CAAC,IAAI;AAAA,MACzG;AAAA,IACF;AACA,QAAI,eAAe,CAAC,QAAQ,YAAY,UAAU,CAAC,GAAG,cAAc,EAAE,OAAO,KAAK,CAAC,GAAG,GAAG;AACzF,QAAI,gBAAgB,CAAC,KAAK,KAAK,UAAU;AACvC,sBAAgB,KAAK,OAAO,QAAQ,WAAW,MAAM,KAAK,KAAK,KAAK;AACpE,aAAO;AAAA,IACT;AACA,QAAI,qBAAqB,CAAC;AAC1B,aAAS,oBAAoB;AAAA,MAC3B,SAAS,MAAM;AAAA,IACjB,CAAC;AACD,WAAO,UAAU,aAAa,kBAAkB;AAChD,QAAI,eAAe,QAAQ,eAAgB;AAC3C,QAAI,eAAe;AACnB,QAAI,kBAAkB;AACtB,QAAM,UAAU;AAChB,QAAM,aAAa;AACnB,QAAM,aAAN,cAAyB,aAAa,UAAU;AAAA,MAC9C,cAAc;AACZ,cAAM,GAAG,SAAS;AAClB,sBAAc,MAAM,cAAc,aAAa,UAAU;AACzD,sBAAc,MAAM,YAAY,IAAI;AACpC,sBAAc,MAAM,eAAe,IAAI;AACvC,sBAAc,MAAM,kBAAkB,IAAI;AAC1C,sBAAc,MAAM,QAAQ,MAAM;AAChC,eAAK,UAAU,CAAC;AAAA,QAClB,CAAC;AACD,sBAAc,MAAM,UAAU,MAAM;AAClC,cAAI,KAAK,MAAM,WAAW,MAAM;AAC9B,iBAAK,UAAU,KAAK,MAAM,MAAM;AAAA,UAClC;AAAA,QACF,CAAC;AACD,sBAAc,MAAM,OAAO,CAAC,WAAW;AACrC,eAAK,SAAS;AAAA,QAChB,CAAC;AAAA,MACH;AAAA,MACA,oBAAoB;AAClB,aAAK,MAAM,WAAW,KAAK,MAAM,QAAQ,IAAI;AAAA,MAC/C;AAAA,MACA,KAAK,KAAK,SAAS;AACjB,SAAC,GAAG,aAAa,QAAQ,SAAS,UAAU,EAAE,KAAK,CAAC,OAAO;AACzD,cAAI,CAAC,KAAK;AACR;AACF,gBAAM,EAAE,MAAM,eAAe,OAAO,QAAQ,MAAM,IAAI,GAAG,OAAO;AAChE,cAAI,CAAC,SAAS;AACZ,iBAAK,SAAS,GAAG,OAAO,KAAK,MAAM;AACnC,iBAAK,OAAO,KAAK,MAAM,KAAK,MAAM,MAAM;AACxC,iBAAK,OAAO,KAAK,OAAO,MAAM;AAC5B,oBAAM,YAAY,KAAK,WAAW,KAAK;AACvC,kBAAI,YAAY,MAAM;AACpB;AAAA,cACF;AACA,mBAAK,MAAM,QAAQ;AAAA,YACrB,CAAC;AACD,iBAAK,OAAO,KAAK,eAAe,CAAC,MAAM;AACrC,mBAAK,cAAc,EAAE,kBAAkB;AACvC,mBAAK,iBAAiB,EAAE;AAAA,YAC1B,CAAC;AACD,iBAAK,OAAO,KAAK,QAAQ,MAAM,KAAK,MAAM,QAAQ,CAAC;AACnD,iBAAK,OAAO,KAAK,OAAO,CAAC,MAAM,KAAK,MAAM,QAAQ,CAAC,CAAC;AAAA,UACtD;AACA,eAAK,OAAO,KAAK,KAAK;AAAA,YACpB,GAAG,KAAK,MAAM,OAAO;AAAA,YACrB,UAAU,MAAM;AACd,mBAAK,OAAO,YAAY,CAAC,aAAa;AACpC,qBAAK,WAAW,WAAW;AAC3B,qBAAK,MAAM,QAAQ;AAAA,cACrB,CAAC;AAAA,YACH;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,MACA,OAAO;AACL,aAAK,WAAW,MAAM;AAAA,MACxB;AAAA,MACA,QAAQ;AACN,aAAK,WAAW,OAAO;AAAA,MACzB;AAAA,MACA,OAAO;AAAA,MACP;AAAA,MACA,OAAO,SAAS,cAAc,MAAM;AAClC,aAAK,WAAW,UAAU,UAAU,GAAG;AACvC,YAAI,CAAC,aAAa;AAChB,eAAK,MAAM;AAAA,QACb;AAAA,MACF;AAAA,MACA,UAAU,UAAU;AAClB,aAAK,WAAW,aAAa,WAAW,GAAG;AAAA,MAC7C;AAAA,MACA,cAAc;AACZ,eAAO,KAAK;AAAA,MACd;AAAA,MACA,iBAAiB;AACf,eAAO,KAAK;AAAA,MACd;AAAA,MACA,mBAAmB;AACjB,eAAO,KAAK,iBAAiB,KAAK;AAAA,MACpC;AAAA,MACA,SAAS;AACP,cAAM,EAAE,QAAQ,IAAI,KAAK;AACzB,cAAM,QAAQ;AAAA,UACZ,OAAO;AAAA,UACP,QAAQ;AAAA,UACR;AAAA,QACF;AACA,eAAuB,aAAa,QAAQ;AAAA,UAC1C;AAAA,UACA;AAAA,YACE,KAAK,KAAK;AAAA,YACV,KAAK,wCAAwC,mBAAmB,KAAK,MAAM,GAAG,CAAC;AAAA,YAC/E;AAAA,YACA,aAAa;AAAA,YACb,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,kBAAc,YAAY,eAAe,YAAY;AACrD,kBAAc,YAAY,WAAW,gBAAgB,QAAQ,UAAU;AACvE,kBAAc,YAAY,eAAe,IAAI;AAAA;AAAA;", "names": []}