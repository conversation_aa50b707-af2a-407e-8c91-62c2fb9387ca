import React from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Grid,
  Link,
  IconButton,
  Divider
} from '@mui/material';
import {
  School as SchoolIcon,
  Facebook,
  Twitter,
  LinkedIn,
  Instagram
} from '@mui/icons-material';

const Footer = () => {
  return (
    <Box
      component="footer"
      sx={{
        bgcolor: 'primary.main',
        color: 'white',
        py: 6,
        mt: 'auto'
      }}
    >
      <Container maxWidth="lg">
        <Grid container spacing={4}>
          {/* Brand Section */}
          <Grid item xs={12} md={4}>
            <Box display="flex" alignItems="center" mb={2}>
              <SchoolIcon sx={{ mr: 1 }} />
              <Typography variant="h6" fontWeight="bold">
                EduLMS
              </Typography>
            </Box>
            <Typography variant="body2" sx={{ mb: 2 }}>
              Empowering education through innovative online learning management system. 
              Learn, grow, and achieve your goals with our comprehensive platform.
            </Typography>
            <Box>
              <IconButton color="inherit" size="small">
                <Facebook />
              </IconButton>
              <IconButton color="inherit" size="small">
                <Twitter />
              </IconButton>
              <IconButton color="inherit" size="small">
                <LinkedIn />
              </IconButton>
              <IconButton color="inherit" size="small">
                <Instagram />
              </IconButton>
            </Box>
          </Grid>

          {/* Quick Links */}
          <Grid item xs={12} sm={6} md={2}>
            <Typography variant="h6" gutterBottom>
              Quick Links
            </Typography>
            <Box>
              <Link href="/" color="inherit" underline="hover" display="block" sx={{ mb: 1 }}>
                Home
              </Link>
              <Link href="/courses" color="inherit" underline="hover" display="block" sx={{ mb: 1 }}>
                Courses
              </Link>
              <Link href="/about" color="inherit" underline="hover" display="block" sx={{ mb: 1 }}>
                About Us
              </Link>
              <Link href="/contact" color="inherit" underline="hover" display="block" sx={{ mb: 1 }}>
                Contact
              </Link>
            </Box>
          </Grid>

          {/* Categories */}
          <Grid item xs={12} sm={6} md={2}>
            <Typography variant="h6" gutterBottom>
              Categories
            </Typography>
            <Box>
              <Link href="/courses?category=Programming" color="inherit" underline="hover" display="block" sx={{ mb: 1 }}>
                Programming
              </Link>
              <Link href="/courses?category=Design" color="inherit" underline="hover" display="block" sx={{ mb: 1 }}>
                Design
              </Link>
              <Link href="/courses?category=Business" color="inherit" underline="hover" display="block" sx={{ mb: 1 }}>
                Business
              </Link>
              <Link href="/courses?category=Marketing" color="inherit" underline="hover" display="block" sx={{ mb: 1 }}>
                Marketing
              </Link>
            </Box>
          </Grid>

          {/* Support */}
          <Grid item xs={12} sm={6} md={2}>
            <Typography variant="h6" gutterBottom>
              Support
            </Typography>
            <Box>
              <Link href="/help" color="inherit" underline="hover" display="block" sx={{ mb: 1 }}>
                Help Center
              </Link>
              <Link href="/faq" color="inherit" underline="hover" display="block" sx={{ mb: 1 }}>
                FAQ
              </Link>
              <Link href="/privacy" color="inherit" underline="hover" display="block" sx={{ mb: 1 }}>
                Privacy Policy
              </Link>
              <Link href="/terms" color="inherit" underline="hover" display="block" sx={{ mb: 1 }}>
                Terms of Service
              </Link>
            </Box>
          </Grid>

          {/* Contact Info */}
          <Grid item xs={12} sm={6} md={2}>
            <Typography variant="h6" gutterBottom>
              Contact
            </Typography>
            <Typography variant="body2" sx={{ mb: 1 }}>
              Email: <EMAIL>
            </Typography>
            <Typography variant="body2" sx={{ mb: 1 }}>
              Phone: +****************
            </Typography>
            <Typography variant="body2">
              Address: 123 Education St, Learning City, LC 12345
            </Typography>
          </Grid>
        </Grid>

        <Divider sx={{ my: 4, bgcolor: 'rgba(255,255,255,0.2)' }} />

        {/* Copyright */}
        <Box
          display="flex"
          justifyContent="space-between"
          alignItems="center"
          flexWrap="wrap"
        >
          <Typography variant="body2">
            © {new Date().getFullYear()} EduLMS. All rights reserved.
          </Typography>
          <Typography variant="body2">
            Built with ❤️ for education
          </Typography>
        </Box>
      </Container>
    </Box>
  );
};

export default Footer;
