import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { Box } from '@mui/material';

const VirtualScrollList = ({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  overscan = 5,
  onScroll,
  className,
  ...props
}) => {
  const [scrollTop, setScrollTop] = useState(0);
  const scrollElementRef = useRef(null);

  // Calculate visible range
  const visibleRange = useMemo(() => {
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
    const endIndex = Math.min(
      items.length - 1,
      Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
    );

    return { startIndex, endIndex };
  }, [scrollTop, itemHeight, containerHeight, items.length, overscan]);

  // Calculate total height and visible items
  const { totalHeight, visibleItems, offsetY } = useMemo(() => {
    const totalHeight = items.length * itemHeight;
    const visibleItems = items.slice(visibleRange.startIndex, visibleRange.endIndex + 1);
    const offsetY = visibleRange.startIndex * itemHeight;

    return { totalHeight, visibleItems, offsetY };
  }, [items, itemHeight, visibleRange]);

  // Handle scroll events
  const handleScroll = useCallback((e) => {
    const newScrollTop = e.target.scrollTop;
    setScrollTop(newScrollTop);
    onScroll?.(e);
  }, [onScroll]);

  // Scroll to specific index
  const scrollToIndex = useCallback((index) => {
    if (scrollElementRef.current) {
      const scrollTop = index * itemHeight;
      scrollElementRef.current.scrollTop = scrollTop;
      setScrollTop(scrollTop);
    }
  }, [itemHeight]);

  // Scroll to top
  const scrollToTop = useCallback(() => {
    scrollToIndex(0);
  }, [scrollToIndex]);

  // Expose scroll methods
  useEffect(() => {
    if (props.ref && typeof props.ref === 'object') {
      props.ref.current = {
        scrollToIndex,
        scrollToTop,
        scrollElement: scrollElementRef.current
      };
    }
  }, [scrollToIndex, scrollToTop, props.ref]);

  return (
    <Box
      ref={scrollElementRef}
      className={className}
      onScroll={handleScroll}
      sx={{
        height: containerHeight,
        overflow: 'auto',
        position: 'relative',
        ...props.sx
      }}
    >
      {/* Total height container */}
      <Box sx={{ height: totalHeight, position: 'relative' }}>
        {/* Visible items container */}
        <Box
          sx={{
            position: 'absolute',
            top: offsetY,
            left: 0,
            right: 0
          }}
        >
          {visibleItems.map((item, index) => {
            const actualIndex = visibleRange.startIndex + index;
            return (
              <Box
                key={item.id || actualIndex}
                sx={{
                  height: itemHeight,
                  overflow: 'hidden'
                }}
              >
                {renderItem(item, actualIndex)}
              </Box>
            );
          })}
        </Box>
      </Box>
    </Box>
  );
};

// Higher-order component for virtual scrolling
export const withVirtualScrolling = (Component, itemHeight = 100) => {
  return React.forwardRef((props, ref) => {
    const { items, containerHeight = 400, ...otherProps } = props;

    const renderItem = useCallback((item, index) => (
      <Component item={item} index={index} {...otherProps} />
    ), [otherProps]);

    return (
      <VirtualScrollList
        ref={ref}
        items={items}
        itemHeight={itemHeight}
        containerHeight={containerHeight}
        renderItem={renderItem}
      />
    );
  });
};

// Hook for virtual scrolling
export const useVirtualScroll = (items, itemHeight, containerHeight) => {
  const [scrollTop, setScrollTop] = useState(0);

  const visibleItems = useMemo(() => {
    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight) + 1,
      items.length
    );

    return {
      startIndex,
      endIndex,
      items: items.slice(startIndex, endIndex),
      totalHeight: items.length * itemHeight,
      offsetY: startIndex * itemHeight
    };
  }, [items, itemHeight, containerHeight, scrollTop]);

  const handleScroll = useCallback((e) => {
    setScrollTop(e.target.scrollTop);
  }, []);

  return { visibleItems, handleScroll };
};

// Infinite scroll component
export const InfiniteScrollList = ({
  items,
  loadMore,
  hasMore,
  loading,
  itemHeight,
  containerHeight,
  renderItem,
  renderLoader,
  threshold = 200,
  ...props
}) => {
  const [scrollTop, setScrollTop] = useState(0);
  const scrollElementRef = useRef(null);

  // Check if we need to load more items
  useEffect(() => {
    if (!hasMore || loading) return;

    const element = scrollElementRef.current;
    if (!element) return;

    const { scrollTop, scrollHeight, clientHeight } = element;
    const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

    if (distanceFromBottom < threshold) {
      loadMore();
    }
  }, [scrollTop, hasMore, loading, loadMore, threshold]);

  const handleScroll = useCallback((e) => {
    setScrollTop(e.target.scrollTop);
  }, []);

  const visibleRange = useMemo(() => {
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - 5);
    const endIndex = Math.min(
      items.length - 1,
      Math.ceil((scrollTop + containerHeight) / itemHeight) + 5
    );

    return { startIndex, endIndex };
  }, [scrollTop, itemHeight, containerHeight, items.length]);

  const { totalHeight, visibleItems, offsetY } = useMemo(() => {
    const totalHeight = items.length * itemHeight + (hasMore ? itemHeight : 0);
    const visibleItems = items.slice(visibleRange.startIndex, visibleRange.endIndex + 1);
    const offsetY = visibleRange.startIndex * itemHeight;

    return { totalHeight, visibleItems, offsetY };
  }, [items, itemHeight, visibleRange, hasMore]);

  return (
    <Box
      ref={scrollElementRef}
      onScroll={handleScroll}
      sx={{
        height: containerHeight,
        overflow: 'auto',
        position: 'relative',
        ...props.sx
      }}
    >
      <Box sx={{ height: totalHeight, position: 'relative' }}>
        <Box
          sx={{
            position: 'absolute',
            top: offsetY,
            left: 0,
            right: 0
          }}
        >
          {visibleItems.map((item, index) => {
            const actualIndex = visibleRange.startIndex + index;
            return (
              <Box
                key={item.id || actualIndex}
                sx={{
                  height: itemHeight,
                  overflow: 'hidden'
                }}
              >
                {renderItem(item, actualIndex)}
              </Box>
            );
          })}
          
          {/* Loader */}
          {hasMore && (
            <Box sx={{ height: itemHeight }}>
              {renderLoader ? renderLoader() : (
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    height: '100%'
                  }}
                >
                  Loading more...
                </Box>
              )}
            </Box>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default VirtualScrollList;
