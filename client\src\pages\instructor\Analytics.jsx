import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  LinearProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Divider
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  School as SchoolIcon,
  AttachMoney as MoneyIcon,
  Star as StarIcon,
  Assignment as AssignmentIcon
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';

const Analytics = () => {
  const { user } = useAuth();
  const [selectedPeriod, setSelectedPeriod] = useState('30');
  const [analyticsData, setAnalyticsData] = useState(null);

  useEffect(() => {
    // Sample analytics data
    const sampleData = {
      overview: {
        totalStudents: 1250,
        totalRevenue: 15750,
        averageRating: 4.7,
        completionRate: 78
      },
      coursePerformance: [
        {
          id: 1,
          title: 'Complete Web Development Bootcamp',
          students: 450,
          revenue: 4500,
          rating: 4.8,
          completionRate: 85,
          enrollmentTrend: '+12%'
        },
        {
          id: 2,
          title: 'Advanced JavaScript Concepts',
          students: 320,
          revenue: 3200,
          rating: 4.9,
          completionRate: 92,
          enrollmentTrend: '+8%'
        },
        {
          id: 3,
          title: 'React Development Masterclass',
          students: 280,
          revenue: 2800,
          rating: 4.6,
          completionRate: 76,
          enrollmentTrend: '+15%'
        },
        {
          id: 4,
          title: 'Node.js Backend Development',
          students: 200,
          revenue: 2000,
          rating: 4.5,
          completionRate: 68,
          enrollmentTrend: '+5%'
        }
      ],
      recentEnrollments: [
        {
          student: 'Alice Johnson',
          course: 'Web Development Bootcamp',
          enrolledAt: '2 hours ago',
          avatar: 'https://i.pravatar.cc/40?img=1'
        },
        {
          student: 'Bob Smith',
          course: 'JavaScript Concepts',
          enrolledAt: '5 hours ago',
          avatar: 'https://i.pravatar.cc/40?img=2'
        },
        {
          student: 'Carol Davis',
          course: 'React Masterclass',
          enrolledAt: '1 day ago',
          avatar: 'https://i.pravatar.cc/40?img=3'
        },
        {
          student: 'David Wilson',
          course: 'Node.js Development',
          enrolledAt: '2 days ago',
          avatar: 'https://i.pravatar.cc/40?img=4'
        }
      ],
      monthlyStats: {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        enrollments: [45, 52, 48, 61, 55, 67],
        revenue: [1200, 1400, 1300, 1650, 1500, 1800]
      }
    };

    setAnalyticsData(sampleData);
  }, [selectedPeriod]);

  const StatCard = ({ icon, title, value, subtitle, color = 'primary', trend }) => (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
          <Avatar sx={{ bgcolor: `${color}.main` }}>
            {icon}
          </Avatar>
          {trend && (
            <Chip
              label={trend}
              size="small"
              color={trend.startsWith('+') ? 'success' : 'error'}
              variant="outlined"
            />
          )}
        </Box>
        <Typography variant="h4" component="div" fontWeight="bold" gutterBottom>
          {value}
        </Typography>
        <Typography variant="h6" color="text.primary" gutterBottom>
          {title}
        </Typography>
        {subtitle && (
          <Typography variant="body2" color="text.secondary">
            {subtitle}
          </Typography>
        )}
      </CardContent>
    </Card>
  );

  if (!analyticsData) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4 }}>
        <LinearProgress />
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
        <Typography variant="h4" component="h1" fontWeight="bold">
          Analytics Dashboard
        </Typography>
        <FormControl sx={{ minWidth: 120 }}>
          <InputLabel>Period</InputLabel>
          <Select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            label="Period"
          >
            <MenuItem value="7">Last 7 days</MenuItem>
            <MenuItem value="30">Last 30 days</MenuItem>
            <MenuItem value="90">Last 3 months</MenuItem>
            <MenuItem value="365">Last year</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {/* Overview Stats */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            icon={<PeopleIcon />}
            title="Total Students"
            value={analyticsData.overview.totalStudents.toLocaleString()}
            subtitle="Across all courses"
            color="primary"
            trend="+12%"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            icon={<MoneyIcon />}
            title="Total Revenue"
            value={`$${analyticsData.overview.totalRevenue.toLocaleString()}`}
            subtitle="This period"
            color="success"
            trend="+8%"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            icon={<StarIcon />}
            title="Average Rating"
            value={analyticsData.overview.averageRating}
            subtitle="From student reviews"
            color="warning"
            trend="+0.2"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            icon={<AssignmentIcon />}
            title="Completion Rate"
            value={`${analyticsData.overview.completionRate}%`}
            subtitle="Course completion"
            color="info"
            trend="+5%"
          />
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Course Performance Table */}
        <Grid item xs={12} lg={8}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Course Performance
            </Typography>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Course</TableCell>
                    <TableCell align="right">Students</TableCell>
                    <TableCell align="right">Revenue</TableCell>
                    <TableCell align="right">Rating</TableCell>
                    <TableCell align="right">Completion</TableCell>
                    <TableCell align="right">Trend</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {analyticsData.coursePerformance.map((course) => (
                    <TableRow key={course.id}>
                      <TableCell>
                        <Typography variant="subtitle2">
                          {course.title}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">{course.students}</TableCell>
                      <TableCell align="right">${course.revenue}</TableCell>
                      <TableCell align="right">
                        <Box display="flex" alignItems="center" justifyContent="flex-end">
                          <StarIcon sx={{ fontSize: 16, color: 'warning.main', mr: 0.5 }} />
                          {course.rating}
                        </Box>
                      </TableCell>
                      <TableCell align="right">
                        <Box sx={{ minWidth: 80 }}>
                          <LinearProgress
                            variant="determinate"
                            value={course.completionRate}
                            sx={{ mb: 0.5 }}
                          />
                          <Typography variant="caption">
                            {course.completionRate}%
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell align="right">
                        <Chip
                          label={course.enrollmentTrend}
                          size="small"
                          color="success"
                          variant="outlined"
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Grid>

        {/* Recent Enrollments */}
        <Grid item xs={12} lg={4}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Recent Enrollments
            </Typography>
            <List>
              {analyticsData.recentEnrollments.map((enrollment, index) => (
                <React.Fragment key={index}>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemAvatar>
                      <Avatar src={enrollment.avatar} />
                    </ListItemAvatar>
                    <ListItemText
                      primary={enrollment.student}
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            {enrollment.course}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {enrollment.enrolledAt}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                  {index < analyticsData.recentEnrollments.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          </Paper>
        </Grid>
      </Grid>

      {/* Revenue Chart Placeholder */}
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12}>
          <Paper sx={{ p: 3, textAlign: 'center', minHeight: 200 }}>
            <Typography variant="h6" gutterBottom>
              Revenue & Enrollment Trends
            </Typography>
            <Typography color="text.secondary">
              Chart visualization would go here
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
              Integration with Chart.js or Recharts for detailed analytics visualization
            </Typography>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default Analytics;
