import React, { useRef, useEffect, useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Avatar,
  Divider
} from '@mui/material';
import {
  Download as DownloadIcon,
  Share as ShareIcon,
  Print as PrintIcon,
  Verified as VerifiedIcon
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';

const CertificateGenerator = ({ 
  course, 
  student, 
  completionDate, 
  grade, 
  open, 
  onClose 
}) => {
  const { user } = useAuth();
  const canvasRef = useRef(null);
  const [certificateData, setCertificateData] = useState({
    template: 'modern',
    backgroundColor: '#ffffff',
    primaryColor: '#1976d2',
    secondaryColor: '#f5f5f5',
    fontFamily: 'Arial',
    includeGrade: true,
    includeQR: true
  });

  const templates = {
    modern: {
      name: 'Modern',
      description: 'Clean and professional design'
    },
    classic: {
      name: 'Classic',
      description: 'Traditional certificate style'
    },
    elegant: {
      name: 'Elegant',
      description: 'Sophisticated design with decorative elements'
    }
  };

  useEffect(() => {
    if (open && course && student) {
      generateCertificate();
    }
  }, [open, course, student, certificateData]);

  const generateCertificate = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    const width = 1200;
    const height = 800;
    
    canvas.width = width;
    canvas.height = height;

    // Clear canvas
    ctx.fillStyle = certificateData.backgroundColor;
    ctx.fillRect(0, 0, width, height);

    // Draw border
    ctx.strokeStyle = certificateData.primaryColor;
    ctx.lineWidth = 8;
    ctx.strokeRect(20, 20, width - 40, height - 40);

    // Inner border
    ctx.strokeStyle = certificateData.secondaryColor;
    ctx.lineWidth = 2;
    ctx.strokeRect(40, 40, width - 80, height - 80);

    // Header
    ctx.fillStyle = certificateData.primaryColor;
    ctx.font = 'bold 48px ' + certificateData.fontFamily;
    ctx.textAlign = 'center';
    ctx.fillText('CERTIFICATE OF COMPLETION', width / 2, 120);

    // Decorative line
    ctx.strokeStyle = certificateData.primaryColor;
    ctx.lineWidth = 3;
    ctx.beginPath();
    ctx.moveTo(width / 2 - 200, 140);
    ctx.lineTo(width / 2 + 200, 140);
    ctx.stroke();

    // "This is to certify that"
    ctx.fillStyle = '#666666';
    ctx.font = '24px ' + certificateData.fontFamily;
    ctx.fillText('This is to certify that', width / 2, 200);

    // Student name
    ctx.fillStyle = certificateData.primaryColor;
    ctx.font = 'bold 56px ' + certificateData.fontFamily;
    ctx.fillText(student.name || `${student.firstName} ${student.lastName}`, width / 2, 280);

    // "has successfully completed"
    ctx.fillStyle = '#666666';
    ctx.font = '24px ' + certificateData.fontFamily;
    ctx.fillText('has successfully completed the course', width / 2, 340);

    // Course title
    ctx.fillStyle = '#333333';
    ctx.font = 'bold 36px ' + certificateData.fontFamily;
    const courseTitle = course.title;
    if (courseTitle.length > 40) {
      // Split long titles
      const words = courseTitle.split(' ');
      const line1 = words.slice(0, Math.ceil(words.length / 2)).join(' ');
      const line2 = words.slice(Math.ceil(words.length / 2)).join(' ');
      ctx.fillText(line1, width / 2, 400);
      ctx.fillText(line2, width / 2, 450);
    } else {
      ctx.fillText(courseTitle, width / 2, 420);
    }

    // Grade (if included)
    if (certificateData.includeGrade && grade) {
      ctx.fillStyle = '#666666';
      ctx.font = '20px ' + certificateData.fontFamily;
      ctx.fillText(`with a grade of ${grade}%`, width / 2, 500);
    }

    // Date
    ctx.fillStyle = '#666666';
    ctx.font = '20px ' + certificateData.fontFamily;
    const dateStr = new Date(completionDate).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
    ctx.fillText(`Completed on ${dateStr}`, width / 2, 550);

    // Signature section
    ctx.fillStyle = '#333333';
    ctx.font = '18px ' + certificateData.fontFamily;
    ctx.textAlign = 'left';
    ctx.fillText('Instructor:', 150, 650);
    ctx.fillText(course.instructor || 'Course Instructor', 150, 680);

    // Signature line
    ctx.strokeStyle = '#cccccc';
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.moveTo(150, 700);
    ctx.lineTo(400, 700);
    ctx.stroke();

    // Institution
    ctx.textAlign = 'right';
    ctx.fillText('EduLMS Platform', width - 150, 650);
    ctx.fillText('Online Learning Management System', width - 150, 680);

    // Institution signature line
    ctx.beginPath();
    ctx.moveTo(width - 400, 700);
    ctx.lineTo(width - 150, 700);
    ctx.stroke();

    // Certificate ID
    ctx.textAlign = 'center';
    ctx.fillStyle = '#999999';
    ctx.font = '14px ' + certificateData.fontFamily;
    const certificateId = `CERT-${course.id}-${student.id}-${Date.now()}`;
    ctx.fillText(`Certificate ID: ${certificateId}`, width / 2, 750);

    // QR Code placeholder (if included)
    if (certificateData.includeQR) {
      ctx.strokeStyle = '#cccccc';
      ctx.lineWidth = 2;
      ctx.strokeRect(50, 600, 80, 80);
      ctx.fillStyle = '#cccccc';
      ctx.font = '12px ' + certificateData.fontFamily;
      ctx.textAlign = 'center';
      ctx.fillText('QR', 90, 645);
      ctx.fillText('Code', 90, 660);
    }

    // Verification seal
    drawVerificationSeal(ctx, width - 120, 120, 60);
  };

  const drawVerificationSeal = (ctx, x, y, radius) => {
    // Outer circle
    ctx.beginPath();
    ctx.arc(x, y, radius, 0, 2 * Math.PI);
    ctx.fillStyle = certificateData.primaryColor;
    ctx.fill();

    // Inner circle
    ctx.beginPath();
    ctx.arc(x, y, radius - 10, 0, 2 * Math.PI);
    ctx.fillStyle = certificateData.backgroundColor;
    ctx.fill();

    // Text
    ctx.fillStyle = certificateData.primaryColor;
    ctx.font = 'bold 12px ' + certificateData.fontFamily;
    ctx.textAlign = 'center';
    ctx.fillText('VERIFIED', x, y - 5);
    ctx.fillText('CERTIFICATE', x, y + 10);
  };

  const downloadCertificate = () => {
    const canvas = canvasRef.current;
    const link = document.createElement('a');
    link.download = `certificate-${course.title.replace(/\s+/g, '-').toLowerCase()}.png`;
    link.href = canvas.toDataURL();
    link.click();
  };

  const printCertificate = () => {
    const canvas = canvasRef.current;
    const dataUrl = canvas.toDataURL();
    const windowContent = `
      <html>
        <head>
          <title>Certificate</title>
          <style>
            body { margin: 0; padding: 20px; text-align: center; }
            img { max-width: 100%; height: auto; }
            @media print {
              body { margin: 0; padding: 0; }
              img { width: 100%; height: auto; }
            }
          </style>
        </head>
        <body>
          <img src="${dataUrl}" alt="Certificate" />
        </body>
      </html>
    `;
    
    const printWindow = window.open('', '_blank');
    printWindow.document.write(windowContent);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
  };

  const shareCertificate = async () => {
    const canvas = canvasRef.current;
    
    if (navigator.share && canvas.toBlob) {
      canvas.toBlob(async (blob) => {
        const file = new File([blob], 'certificate.png', { type: 'image/png' });
        try {
          await navigator.share({
            title: 'My Course Certificate',
            text: `I've completed ${course.title}!`,
            files: [file]
          });
        } catch (error) {
          console.error('Error sharing:', error);
          fallbackShare();
        }
      });
    } else {
      fallbackShare();
    }
  };

  const fallbackShare = () => {
    const canvas = canvasRef.current;
    const dataUrl = canvas.toDataURL();
    
    // Copy to clipboard or show share dialog
    if (navigator.clipboard) {
      canvas.toBlob(async (blob) => {
        try {
          await navigator.clipboard.write([
            new ClipboardItem({ 'image/png': blob })
          ]);
          alert('Certificate copied to clipboard!');
        } catch (error) {
          console.error('Error copying to clipboard:', error);
          // Fallback: open in new window
          window.open(dataUrl, '_blank');
        }
      });
    } else {
      window.open(dataUrl, '_blank');
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle>
        <Box display="flex" alignItems="center" gap={1}>
          <VerifiedIcon color="primary" />
          Certificate of Completion
        </Box>
      </DialogTitle>
      
      <DialogContent>
        <Grid container spacing={3}>
          {/* Certificate Preview */}
          <Grid item xs={12} md={8}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <canvas
                ref={canvasRef}
                style={{
                  maxWidth: '100%',
                  height: 'auto',
                  border: '1px solid #ddd',
                  borderRadius: '8px'
                }}
              />
            </Paper>
          </Grid>

          {/* Customization Options */}
          <Grid item xs={12} md={4}>
            <Paper sx={{ p: 2 }}>
              <Typography variant="h6" gutterBottom>
                Customize Certificate
              </Typography>

              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Template</InputLabel>
                <Select
                  value={certificateData.template}
                  onChange={(e) => setCertificateData(prev => ({
                    ...prev,
                    template: e.target.value
                  }))}
                  label="Template"
                >
                  {Object.entries(templates).map(([key, template]) => (
                    <MenuItem key={key} value={key}>
                      {template.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <TextField
                fullWidth
                label="Primary Color"
                type="color"
                value={certificateData.primaryColor}
                onChange={(e) => setCertificateData(prev => ({
                  ...prev,
                  primaryColor: e.target.value
                }))}
                sx={{ mb: 2 }}
              />

              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Font Family</InputLabel>
                <Select
                  value={certificateData.fontFamily}
                  onChange={(e) => setCertificateData(prev => ({
                    ...prev,
                    fontFamily: e.target.value
                  }))}
                  label="Font Family"
                >
                  <MenuItem value="Arial">Arial</MenuItem>
                  <MenuItem value="Times New Roman">Times New Roman</MenuItem>
                  <MenuItem value="Georgia">Georgia</MenuItem>
                  <MenuItem value="Helvetica">Helvetica</MenuItem>
                </Select>
              </FormControl>

              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Options
                </Typography>
                <Box display="flex" flexDirection="column" gap={1}>
                  <Chip
                    label="Include Grade"
                    color={certificateData.includeGrade ? 'primary' : 'default'}
                    onClick={() => setCertificateData(prev => ({
                      ...prev,
                      includeGrade: !prev.includeGrade
                    }))}
                    clickable
                  />
                  <Chip
                    label="Include QR Code"
                    color={certificateData.includeQR ? 'primary' : 'default'}
                    onClick={() => setCertificateData(prev => ({
                      ...prev,
                      includeQR: !prev.includeQR
                    }))}
                    clickable
                  />
                </Box>
              </Box>

              <Divider sx={{ my: 2 }} />

              <Typography variant="subtitle2" gutterBottom>
                Certificate Details
              </Typography>
              <Box sx={{ mb: 1 }}>
                <Typography variant="body2" color="text.secondary">
                  Student: {student?.firstName} {student?.lastName}
                </Typography>
              </Box>
              <Box sx={{ mb: 1 }}>
                <Typography variant="body2" color="text.secondary">
                  Course: {course?.title}
                </Typography>
              </Box>
              <Box sx={{ mb: 1 }}>
                <Typography variant="body2" color="text.secondary">
                  Completion Date: {new Date(completionDate).toLocaleDateString()}
                </Typography>
              </Box>
              {grade && (
                <Box sx={{ mb: 1 }}>
                  <Typography variant="body2" color="text.secondary">
                    Grade: {grade}%
                  </Typography>
                </Box>
              )}
            </Paper>
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>
          Close
        </Button>
        <Button
          onClick={printCertificate}
          startIcon={<PrintIcon />}
          variant="outlined"
        >
          Print
        </Button>
        <Button
          onClick={shareCertificate}
          startIcon={<ShareIcon />}
          variant="outlined"
        >
          Share
        </Button>
        <Button
          onClick={downloadCertificate}
          startIcon={<DownloadIcon />}
          variant="contained"
        >
          Download
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CertificateGenerator;
