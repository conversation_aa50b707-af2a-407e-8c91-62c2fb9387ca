{"version": 3, "sources": ["../../react-player/lib/players/Vidyard.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar Vidyard_exports = {};\n__export(Vidyard_exports, {\n  default: () => Vidyard\n});\nmodule.exports = __toCommonJS(Vidyard_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_utils = require(\"../utils\");\nvar import_patterns = require(\"../patterns\");\nconst SDK_URL = \"https://play.vidyard.com/embed/v4.js\";\nconst SDK_GLOBAL = \"VidyardV4\";\nconst SDK_GLOBAL_READY = \"onVidyardAPI\";\nclass Vidyard extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    __publicField(this, \"callPlayer\", import_utils.callPlayer);\n    __publicField(this, \"mute\", () => {\n      this.setVolume(0);\n    });\n    __publicField(this, \"unmute\", () => {\n      if (this.props.volume !== null) {\n        this.setVolume(this.props.volume);\n      }\n    });\n    __publicField(this, \"ref\", (container) => {\n      this.container = container;\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n  }\n  load(url) {\n    const { playing, config, onError, onDuration } = this.props;\n    const id = url && url.match(import_patterns.MATCH_URL_VIDYARD)[1];\n    if (this.player) {\n      this.stop();\n    }\n    (0, import_utils.getSDK)(SDK_URL, SDK_GLOBAL, SDK_GLOBAL_READY).then((Vidyard2) => {\n      if (!this.container)\n        return;\n      Vidyard2.api.addReadyListener((data, player) => {\n        if (this.player) {\n          return;\n        }\n        this.player = player;\n        this.player.on(\"ready\", this.props.onReady);\n        this.player.on(\"play\", this.props.onPlay);\n        this.player.on(\"pause\", this.props.onPause);\n        this.player.on(\"seek\", this.props.onSeek);\n        this.player.on(\"playerComplete\", this.props.onEnded);\n      }, id);\n      Vidyard2.api.renderPlayer({\n        uuid: id,\n        container: this.container,\n        autoplay: playing ? 1 : 0,\n        ...config.options\n      });\n      Vidyard2.api.getPlayerMetadata(id).then((meta) => {\n        this.duration = meta.length_in_seconds;\n        onDuration(meta.length_in_seconds);\n      });\n    }, onError);\n  }\n  play() {\n    this.callPlayer(\"play\");\n  }\n  pause() {\n    this.callPlayer(\"pause\");\n  }\n  stop() {\n    window.VidyardV4.api.destroyPlayer(this.player);\n  }\n  seekTo(amount, keepPlaying = true) {\n    this.callPlayer(\"seek\", amount);\n    if (!keepPlaying) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n    this.callPlayer(\"setVolume\", fraction);\n  }\n  setPlaybackRate(rate) {\n    this.callPlayer(\"setPlaybackSpeed\", rate);\n  }\n  getDuration() {\n    return this.duration;\n  }\n  getCurrentTime() {\n    return this.callPlayer(\"currentTime\");\n  }\n  getSecondsLoaded() {\n    return null;\n  }\n  render() {\n    const { display } = this.props;\n    const style = {\n      width: \"100%\",\n      height: \"100%\",\n      display\n    };\n    return /* @__PURE__ */ import_react.default.createElement(\"div\", { style }, /* @__PURE__ */ import_react.default.createElement(\"div\", { ref: this.ref }));\n  }\n}\n__publicField(Vidyard, \"displayName\", \"Vidyard\");\n__publicField(Vidyard, \"canPlay\", import_patterns.canPlay.vidyard);\n"], "mappings": ";;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,WAAW,OAAO;AACtB,QAAI,YAAY,OAAO;AACvB,QAAI,mBAAmB,OAAO;AAC9B,QAAI,oBAAoB,OAAO;AAC/B,QAAI,eAAe,OAAO;AAC1B,QAAI,eAAe,OAAO,UAAU;AACpC,QAAI,kBAAkB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,UAAU,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC1J,QAAI,WAAW,CAAC,QAAQ,QAAQ;AAC9B,eAAS,QAAQ;AACf,kBAAU,QAAQ,MAAM,EAAE,KAAK,IAAI,IAAI,GAAG,YAAY,KAAK,CAAC;AAAA,IAChE;AACA,QAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,UAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,iBAAS,OAAO,kBAAkB,IAAI;AACpC,cAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,sBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,MACvH;AACA,aAAO;AAAA,IACT;AACA,QAAI,UAAU,CAAC,KAAK,YAAY,YAAY,SAAS,OAAO,OAAO,SAAS,aAAa,GAAG,CAAC,IAAI,CAAC,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA,MAKnG,cAAc,CAAC,OAAO,CAAC,IAAI,aAAa,UAAU,QAAQ,WAAW,EAAE,OAAO,KAAK,YAAY,KAAK,CAAC,IAAI;AAAA,MACzG;AAAA,IACF;AACA,QAAI,eAAe,CAAC,QAAQ,YAAY,UAAU,CAAC,GAAG,cAAc,EAAE,OAAO,KAAK,CAAC,GAAG,GAAG;AACzF,QAAI,gBAAgB,CAAC,KAAK,KAAK,UAAU;AACvC,sBAAgB,KAAK,OAAO,QAAQ,WAAW,MAAM,KAAK,KAAK,KAAK;AACpE,aAAO;AAAA,IACT;AACA,QAAI,kBAAkB,CAAC;AACvB,aAAS,iBAAiB;AAAA,MACxB,SAAS,MAAM;AAAA,IACjB,CAAC;AACD,WAAO,UAAU,aAAa,eAAe;AAC7C,QAAI,eAAe,QAAQ,eAAgB;AAC3C,QAAI,eAAe;AACnB,QAAI,kBAAkB;AACtB,QAAM,UAAU;AAChB,QAAM,aAAa;AACnB,QAAM,mBAAmB;AACzB,QAAM,UAAN,cAAsB,aAAa,UAAU;AAAA,MAC3C,cAAc;AACZ,cAAM,GAAG,SAAS;AAClB,sBAAc,MAAM,cAAc,aAAa,UAAU;AACzD,sBAAc,MAAM,QAAQ,MAAM;AAChC,eAAK,UAAU,CAAC;AAAA,QAClB,CAAC;AACD,sBAAc,MAAM,UAAU,MAAM;AAClC,cAAI,KAAK,MAAM,WAAW,MAAM;AAC9B,iBAAK,UAAU,KAAK,MAAM,MAAM;AAAA,UAClC;AAAA,QACF,CAAC;AACD,sBAAc,MAAM,OAAO,CAAC,cAAc;AACxC,eAAK,YAAY;AAAA,QACnB,CAAC;AAAA,MACH;AAAA,MACA,oBAAoB;AAClB,aAAK,MAAM,WAAW,KAAK,MAAM,QAAQ,IAAI;AAAA,MAC/C;AAAA,MACA,KAAK,KAAK;AACR,cAAM,EAAE,SAAS,QAAQ,SAAS,WAAW,IAAI,KAAK;AACtD,cAAM,KAAK,OAAO,IAAI,MAAM,gBAAgB,iBAAiB,EAAE,CAAC;AAChE,YAAI,KAAK,QAAQ;AACf,eAAK,KAAK;AAAA,QACZ;AACA,SAAC,GAAG,aAAa,QAAQ,SAAS,YAAY,gBAAgB,EAAE,KAAK,CAAC,aAAa;AACjF,cAAI,CAAC,KAAK;AACR;AACF,mBAAS,IAAI,iBAAiB,CAAC,MAAM,WAAW;AAC9C,gBAAI,KAAK,QAAQ;AACf;AAAA,YACF;AACA,iBAAK,SAAS;AACd,iBAAK,OAAO,GAAG,SAAS,KAAK,MAAM,OAAO;AAC1C,iBAAK,OAAO,GAAG,QAAQ,KAAK,MAAM,MAAM;AACxC,iBAAK,OAAO,GAAG,SAAS,KAAK,MAAM,OAAO;AAC1C,iBAAK,OAAO,GAAG,QAAQ,KAAK,MAAM,MAAM;AACxC,iBAAK,OAAO,GAAG,kBAAkB,KAAK,MAAM,OAAO;AAAA,UACrD,GAAG,EAAE;AACL,mBAAS,IAAI,aAAa;AAAA,YACxB,MAAM;AAAA,YACN,WAAW,KAAK;AAAA,YAChB,UAAU,UAAU,IAAI;AAAA,YACxB,GAAG,OAAO;AAAA,UACZ,CAAC;AACD,mBAAS,IAAI,kBAAkB,EAAE,EAAE,KAAK,CAAC,SAAS;AAChD,iBAAK,WAAW,KAAK;AACrB,uBAAW,KAAK,iBAAiB;AAAA,UACnC,CAAC;AAAA,QACH,GAAG,OAAO;AAAA,MACZ;AAAA,MACA,OAAO;AACL,aAAK,WAAW,MAAM;AAAA,MACxB;AAAA,MACA,QAAQ;AACN,aAAK,WAAW,OAAO;AAAA,MACzB;AAAA,MACA,OAAO;AACL,eAAO,UAAU,IAAI,cAAc,KAAK,MAAM;AAAA,MAChD;AAAA,MACA,OAAO,QAAQ,cAAc,MAAM;AACjC,aAAK,WAAW,QAAQ,MAAM;AAC9B,YAAI,CAAC,aAAa;AAChB,eAAK,MAAM;AAAA,QACb;AAAA,MACF;AAAA,MACA,UAAU,UAAU;AAClB,aAAK,WAAW,aAAa,QAAQ;AAAA,MACvC;AAAA,MACA,gBAAgB,MAAM;AACpB,aAAK,WAAW,oBAAoB,IAAI;AAAA,MAC1C;AAAA,MACA,cAAc;AACZ,eAAO,KAAK;AAAA,MACd;AAAA,MACA,iBAAiB;AACf,eAAO,KAAK,WAAW,aAAa;AAAA,MACtC;AAAA,MACA,mBAAmB;AACjB,eAAO;AAAA,MACT;AAAA,MACA,SAAS;AACP,cAAM,EAAE,QAAQ,IAAI,KAAK;AACzB,cAAM,QAAQ;AAAA,UACZ,OAAO;AAAA,UACP,QAAQ;AAAA,UACR;AAAA,QACF;AACA,eAAuB,aAAa,QAAQ,cAAc,OAAO,EAAE,MAAM,GAAmB,aAAa,QAAQ,cAAc,OAAO,EAAE,KAAK,KAAK,IAAI,CAAC,CAAC;AAAA,MAC1J;AAAA,IACF;AACA,kBAAc,SAAS,eAAe,SAAS;AAC/C,kBAAc,SAAS,WAAW,gBAAgB,QAAQ,OAAO;AAAA;AAAA;", "names": []}