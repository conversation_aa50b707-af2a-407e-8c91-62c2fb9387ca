{"version": 3, "sources": ["../../react-player/lib/players/YouTube.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar YouTube_exports = {};\n__export(YouTube_exports, {\n  default: () => YouTube\n});\nmodule.exports = __toCommonJS(YouTube_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_utils = require(\"../utils\");\nvar import_patterns = require(\"../patterns\");\nconst SDK_URL = \"https://www.youtube.com/iframe_api\";\nconst SDK_GLOBAL = \"YT\";\nconst SDK_GLOBAL_READY = \"onYouTubeIframeAPIReady\";\nconst MATCH_PLAYLIST = /[?&](?:list|channel)=([a-zA-Z0-9_-]+)/;\nconst MATCH_USER_UPLOADS = /user\\/([a-zA-Z0-9_-]+)\\/?/;\nconst MATCH_NOCOOKIE = /youtube-nocookie\\.com/;\nconst NOCOOKIE_HOST = \"https://www.youtube-nocookie.com\";\nclass YouTube extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    __publicField(this, \"callPlayer\", import_utils.callPlayer);\n    __publicField(this, \"parsePlaylist\", (url) => {\n      if (url instanceof Array) {\n        return {\n          listType: \"playlist\",\n          playlist: url.map(this.getID).join(\",\")\n        };\n      }\n      if (MATCH_PLAYLIST.test(url)) {\n        const [, playlistId] = url.match(MATCH_PLAYLIST);\n        return {\n          listType: \"playlist\",\n          list: playlistId.replace(/^UC/, \"UU\")\n        };\n      }\n      if (MATCH_USER_UPLOADS.test(url)) {\n        const [, username] = url.match(MATCH_USER_UPLOADS);\n        return {\n          listType: \"user_uploads\",\n          list: username\n        };\n      }\n      return {};\n    });\n    __publicField(this, \"onStateChange\", (event) => {\n      const { data } = event;\n      const { onPlay, onPause, onBuffer, onBufferEnd, onEnded, onReady, loop, config: { playerVars, onUnstarted } } = this.props;\n      const { UNSTARTED, PLAYING, PAUSED, BUFFERING, ENDED, CUED } = window[SDK_GLOBAL].PlayerState;\n      if (data === UNSTARTED)\n        onUnstarted();\n      if (data === PLAYING) {\n        onPlay();\n        onBufferEnd();\n      }\n      if (data === PAUSED)\n        onPause();\n      if (data === BUFFERING)\n        onBuffer();\n      if (data === ENDED) {\n        const isPlaylist = !!this.callPlayer(\"getPlaylist\");\n        if (loop && !isPlaylist) {\n          if (playerVars.start) {\n            this.seekTo(playerVars.start);\n          } else {\n            this.play();\n          }\n        }\n        onEnded();\n      }\n      if (data === CUED)\n        onReady();\n    });\n    __publicField(this, \"mute\", () => {\n      this.callPlayer(\"mute\");\n    });\n    __publicField(this, \"unmute\", () => {\n      this.callPlayer(\"unMute\");\n    });\n    __publicField(this, \"ref\", (container) => {\n      this.container = container;\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n  }\n  getID(url) {\n    if (!url || url instanceof Array || MATCH_PLAYLIST.test(url)) {\n      return null;\n    }\n    return url.match(import_patterns.MATCH_URL_YOUTUBE)[1];\n  }\n  load(url, isReady) {\n    const { playing, muted, playsinline, controls, loop, config, onError } = this.props;\n    const { playerVars, embedOptions } = config;\n    const id = this.getID(url);\n    if (isReady) {\n      if (MATCH_PLAYLIST.test(url) || MATCH_USER_UPLOADS.test(url) || url instanceof Array) {\n        this.player.loadPlaylist(this.parsePlaylist(url));\n        return;\n      }\n      this.player.cueVideoById({\n        videoId: id,\n        startSeconds: (0, import_utils.parseStartTime)(url) || playerVars.start,\n        endSeconds: (0, import_utils.parseEndTime)(url) || playerVars.end\n      });\n      return;\n    }\n    (0, import_utils.getSDK)(SDK_URL, SDK_GLOBAL, SDK_GLOBAL_READY, (YT) => YT.loaded).then((YT) => {\n      if (!this.container)\n        return;\n      this.player = new YT.Player(this.container, {\n        width: \"100%\",\n        height: \"100%\",\n        videoId: id,\n        playerVars: {\n          autoplay: playing ? 1 : 0,\n          mute: muted ? 1 : 0,\n          controls: controls ? 1 : 0,\n          start: (0, import_utils.parseStartTime)(url),\n          end: (0, import_utils.parseEndTime)(url),\n          origin: window.location.origin,\n          playsinline: playsinline ? 1 : 0,\n          ...this.parsePlaylist(url),\n          ...playerVars\n        },\n        events: {\n          onReady: () => {\n            if (loop) {\n              this.player.setLoop(true);\n            }\n            this.props.onReady();\n          },\n          onPlaybackRateChange: (event) => this.props.onPlaybackRateChange(event.data),\n          onPlaybackQualityChange: (event) => this.props.onPlaybackQualityChange(event),\n          onStateChange: this.onStateChange,\n          onError: (event) => onError(event.data)\n        },\n        host: MATCH_NOCOOKIE.test(url) ? NOCOOKIE_HOST : void 0,\n        ...embedOptions\n      });\n    }, onError);\n    if (embedOptions.events) {\n      console.warn(\"Using `embedOptions.events` will likely break things. Use ReactPlayer\\u2019s callback props instead, eg onReady, onPlay, onPause\");\n    }\n  }\n  play() {\n    this.callPlayer(\"playVideo\");\n  }\n  pause() {\n    this.callPlayer(\"pauseVideo\");\n  }\n  stop() {\n    if (!document.body.contains(this.callPlayer(\"getIframe\")))\n      return;\n    this.callPlayer(\"stopVideo\");\n  }\n  seekTo(amount, keepPlaying = false) {\n    this.callPlayer(\"seekTo\", amount);\n    if (!keepPlaying && !this.props.playing) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n    this.callPlayer(\"setVolume\", fraction * 100);\n  }\n  setPlaybackRate(rate) {\n    this.callPlayer(\"setPlaybackRate\", rate);\n  }\n  setLoop(loop) {\n    this.callPlayer(\"setLoop\", loop);\n  }\n  getDuration() {\n    return this.callPlayer(\"getDuration\");\n  }\n  getCurrentTime() {\n    return this.callPlayer(\"getCurrentTime\");\n  }\n  getSecondsLoaded() {\n    return this.callPlayer(\"getVideoLoadedFraction\") * this.getDuration();\n  }\n  render() {\n    const { display } = this.props;\n    const style = {\n      width: \"100%\",\n      height: \"100%\",\n      display\n    };\n    return /* @__PURE__ */ import_react.default.createElement(\"div\", { style }, /* @__PURE__ */ import_react.default.createElement(\"div\", { ref: this.ref }));\n  }\n}\n__publicField(YouTube, \"displayName\", \"YouTube\");\n__publicField(YouTube, \"canPlay\", import_patterns.canPlay.youtube);\n"], "mappings": ";;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,WAAW,OAAO;AACtB,QAAI,YAAY,OAAO;AACvB,QAAI,mBAAmB,OAAO;AAC9B,QAAI,oBAAoB,OAAO;AAC/B,QAAI,eAAe,OAAO;AAC1B,QAAI,eAAe,OAAO,UAAU;AACpC,QAAI,kBAAkB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,UAAU,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC1J,QAAI,WAAW,CAAC,QAAQ,QAAQ;AAC9B,eAAS,QAAQ;AACf,kBAAU,QAAQ,MAAM,EAAE,KAAK,IAAI,IAAI,GAAG,YAAY,KAAK,CAAC;AAAA,IAChE;AACA,QAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,UAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,iBAAS,OAAO,kBAAkB,IAAI;AACpC,cAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,sBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,MACvH;AACA,aAAO;AAAA,IACT;AACA,QAAI,UAAU,CAAC,KAAK,YAAY,YAAY,SAAS,OAAO,OAAO,SAAS,aAAa,GAAG,CAAC,IAAI,CAAC,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA,MAKnG,cAAc,CAAC,OAAO,CAAC,IAAI,aAAa,UAAU,QAAQ,WAAW,EAAE,OAAO,KAAK,YAAY,KAAK,CAAC,IAAI;AAAA,MACzG;AAAA,IACF;AACA,QAAI,eAAe,CAAC,QAAQ,YAAY,UAAU,CAAC,GAAG,cAAc,EAAE,OAAO,KAAK,CAAC,GAAG,GAAG;AACzF,QAAI,gBAAgB,CAAC,KAAK,KAAK,UAAU;AACvC,sBAAgB,KAAK,OAAO,QAAQ,WAAW,MAAM,KAAK,KAAK,KAAK;AACpE,aAAO;AAAA,IACT;AACA,QAAI,kBAAkB,CAAC;AACvB,aAAS,iBAAiB;AAAA,MACxB,SAAS,MAAM;AAAA,IACjB,CAAC;AACD,WAAO,UAAU,aAAa,eAAe;AAC7C,QAAI,eAAe,QAAQ,eAAgB;AAC3C,QAAI,eAAe;AACnB,QAAI,kBAAkB;AACtB,QAAM,UAAU;AAChB,QAAM,aAAa;AACnB,QAAM,mBAAmB;AACzB,QAAM,iBAAiB;AACvB,QAAM,qBAAqB;AAC3B,QAAM,iBAAiB;AACvB,QAAM,gBAAgB;AACtB,QAAM,UAAN,cAAsB,aAAa,UAAU;AAAA,MAC3C,cAAc;AACZ,cAAM,GAAG,SAAS;AAClB,sBAAc,MAAM,cAAc,aAAa,UAAU;AACzD,sBAAc,MAAM,iBAAiB,CAAC,QAAQ;AAC5C,cAAI,eAAe,OAAO;AACxB,mBAAO;AAAA,cACL,UAAU;AAAA,cACV,UAAU,IAAI,IAAI,KAAK,KAAK,EAAE,KAAK,GAAG;AAAA,YACxC;AAAA,UACF;AACA,cAAI,eAAe,KAAK,GAAG,GAAG;AAC5B,kBAAM,CAAC,EAAE,UAAU,IAAI,IAAI,MAAM,cAAc;AAC/C,mBAAO;AAAA,cACL,UAAU;AAAA,cACV,MAAM,WAAW,QAAQ,OAAO,IAAI;AAAA,YACtC;AAAA,UACF;AACA,cAAI,mBAAmB,KAAK,GAAG,GAAG;AAChC,kBAAM,CAAC,EAAE,QAAQ,IAAI,IAAI,MAAM,kBAAkB;AACjD,mBAAO;AAAA,cACL,UAAU;AAAA,cACV,MAAM;AAAA,YACR;AAAA,UACF;AACA,iBAAO,CAAC;AAAA,QACV,CAAC;AACD,sBAAc,MAAM,iBAAiB,CAAC,UAAU;AAC9C,gBAAM,EAAE,KAAK,IAAI;AACjB,gBAAM,EAAE,QAAQ,SAAS,UAAU,aAAa,SAAS,SAAS,MAAM,QAAQ,EAAE,YAAY,YAAY,EAAE,IAAI,KAAK;AACrH,gBAAM,EAAE,WAAW,SAAS,QAAQ,WAAW,OAAO,KAAK,IAAI,OAAO,UAAU,EAAE;AAClF,cAAI,SAAS;AACX,wBAAY;AACd,cAAI,SAAS,SAAS;AACpB,mBAAO;AACP,wBAAY;AAAA,UACd;AACA,cAAI,SAAS;AACX,oBAAQ;AACV,cAAI,SAAS;AACX,qBAAS;AACX,cAAI,SAAS,OAAO;AAClB,kBAAM,aAAa,CAAC,CAAC,KAAK,WAAW,aAAa;AAClD,gBAAI,QAAQ,CAAC,YAAY;AACvB,kBAAI,WAAW,OAAO;AACpB,qBAAK,OAAO,WAAW,KAAK;AAAA,cAC9B,OAAO;AACL,qBAAK,KAAK;AAAA,cACZ;AAAA,YACF;AACA,oBAAQ;AAAA,UACV;AACA,cAAI,SAAS;AACX,oBAAQ;AAAA,QACZ,CAAC;AACD,sBAAc,MAAM,QAAQ,MAAM;AAChC,eAAK,WAAW,MAAM;AAAA,QACxB,CAAC;AACD,sBAAc,MAAM,UAAU,MAAM;AAClC,eAAK,WAAW,QAAQ;AAAA,QAC1B,CAAC;AACD,sBAAc,MAAM,OAAO,CAAC,cAAc;AACxC,eAAK,YAAY;AAAA,QACnB,CAAC;AAAA,MACH;AAAA,MACA,oBAAoB;AAClB,aAAK,MAAM,WAAW,KAAK,MAAM,QAAQ,IAAI;AAAA,MAC/C;AAAA,MACA,MAAM,KAAK;AACT,YAAI,CAAC,OAAO,eAAe,SAAS,eAAe,KAAK,GAAG,GAAG;AAC5D,iBAAO;AAAA,QACT;AACA,eAAO,IAAI,MAAM,gBAAgB,iBAAiB,EAAE,CAAC;AAAA,MACvD;AAAA,MACA,KAAK,KAAK,SAAS;AACjB,cAAM,EAAE,SAAS,OAAO,aAAa,UAAU,MAAM,QAAQ,QAAQ,IAAI,KAAK;AAC9E,cAAM,EAAE,YAAY,aAAa,IAAI;AACrC,cAAM,KAAK,KAAK,MAAM,GAAG;AACzB,YAAI,SAAS;AACX,cAAI,eAAe,KAAK,GAAG,KAAK,mBAAmB,KAAK,GAAG,KAAK,eAAe,OAAO;AACpF,iBAAK,OAAO,aAAa,KAAK,cAAc,GAAG,CAAC;AAChD;AAAA,UACF;AACA,eAAK,OAAO,aAAa;AAAA,YACvB,SAAS;AAAA,YACT,eAAe,GAAG,aAAa,gBAAgB,GAAG,KAAK,WAAW;AAAA,YAClE,aAAa,GAAG,aAAa,cAAc,GAAG,KAAK,WAAW;AAAA,UAChE,CAAC;AACD;AAAA,QACF;AACA,SAAC,GAAG,aAAa,QAAQ,SAAS,YAAY,kBAAkB,CAAC,OAAO,GAAG,MAAM,EAAE,KAAK,CAAC,OAAO;AAC9F,cAAI,CAAC,KAAK;AACR;AACF,eAAK,SAAS,IAAI,GAAG,OAAO,KAAK,WAAW;AAAA,YAC1C,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,SAAS;AAAA,YACT,YAAY;AAAA,cACV,UAAU,UAAU,IAAI;AAAA,cACxB,MAAM,QAAQ,IAAI;AAAA,cAClB,UAAU,WAAW,IAAI;AAAA,cACzB,QAAQ,GAAG,aAAa,gBAAgB,GAAG;AAAA,cAC3C,MAAM,GAAG,aAAa,cAAc,GAAG;AAAA,cACvC,QAAQ,OAAO,SAAS;AAAA,cACxB,aAAa,cAAc,IAAI;AAAA,cAC/B,GAAG,KAAK,cAAc,GAAG;AAAA,cACzB,GAAG;AAAA,YACL;AAAA,YACA,QAAQ;AAAA,cACN,SAAS,MAAM;AACb,oBAAI,MAAM;AACR,uBAAK,OAAO,QAAQ,IAAI;AAAA,gBAC1B;AACA,qBAAK,MAAM,QAAQ;AAAA,cACrB;AAAA,cACA,sBAAsB,CAAC,UAAU,KAAK,MAAM,qBAAqB,MAAM,IAAI;AAAA,cAC3E,yBAAyB,CAAC,UAAU,KAAK,MAAM,wBAAwB,KAAK;AAAA,cAC5E,eAAe,KAAK;AAAA,cACpB,SAAS,CAAC,UAAU,QAAQ,MAAM,IAAI;AAAA,YACxC;AAAA,YACA,MAAM,eAAe,KAAK,GAAG,IAAI,gBAAgB;AAAA,YACjD,GAAG;AAAA,UACL,CAAC;AAAA,QACH,GAAG,OAAO;AACV,YAAI,aAAa,QAAQ;AACvB,kBAAQ,KAAK,6HAAkI;AAAA,QACjJ;AAAA,MACF;AAAA,MACA,OAAO;AACL,aAAK,WAAW,WAAW;AAAA,MAC7B;AAAA,MACA,QAAQ;AACN,aAAK,WAAW,YAAY;AAAA,MAC9B;AAAA,MACA,OAAO;AACL,YAAI,CAAC,SAAS,KAAK,SAAS,KAAK,WAAW,WAAW,CAAC;AACtD;AACF,aAAK,WAAW,WAAW;AAAA,MAC7B;AAAA,MACA,OAAO,QAAQ,cAAc,OAAO;AAClC,aAAK,WAAW,UAAU,MAAM;AAChC,YAAI,CAAC,eAAe,CAAC,KAAK,MAAM,SAAS;AACvC,eAAK,MAAM;AAAA,QACb;AAAA,MACF;AAAA,MACA,UAAU,UAAU;AAClB,aAAK,WAAW,aAAa,WAAW,GAAG;AAAA,MAC7C;AAAA,MACA,gBAAgB,MAAM;AACpB,aAAK,WAAW,mBAAmB,IAAI;AAAA,MACzC;AAAA,MACA,QAAQ,MAAM;AACZ,aAAK,WAAW,WAAW,IAAI;AAAA,MACjC;AAAA,MACA,cAAc;AACZ,eAAO,KAAK,WAAW,aAAa;AAAA,MACtC;AAAA,MACA,iBAAiB;AACf,eAAO,KAAK,WAAW,gBAAgB;AAAA,MACzC;AAAA,MACA,mBAAmB;AACjB,eAAO,KAAK,WAAW,wBAAwB,IAAI,KAAK,YAAY;AAAA,MACtE;AAAA,MACA,SAAS;AACP,cAAM,EAAE,QAAQ,IAAI,KAAK;AACzB,cAAM,QAAQ;AAAA,UACZ,OAAO;AAAA,UACP,QAAQ;AAAA,UACR;AAAA,QACF;AACA,eAAuB,aAAa,QAAQ,cAAc,OAAO,EAAE,MAAM,GAAmB,aAAa,QAAQ,cAAc,OAAO,EAAE,KAAK,KAAK,IAAI,CAAC,CAAC;AAAA,MAC1J;AAAA,IACF;AACA,kBAAc,SAAS,eAAe,SAAS;AAC/C,kBAAc,SAAS,WAAW,gBAAgB,QAAQ,OAAO;AAAA;AAAA;", "names": []}