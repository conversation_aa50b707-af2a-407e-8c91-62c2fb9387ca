{"name": "lms-server", "version": "1.0.0", "description": "Backend server for Online Learning Management System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed": "node seeders/seedDatabase.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["lms", "education", "mern", "nodejs"], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"bcryptjs": "^3.0.2", "cloudinary": "^2.7.0", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "express-fileupload": "^1.5.1", "express-rate-limit": "^7.5.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.0", "morgan": "^1.10.0", "multer": "^2.0.1", "socket.io": "^4.8.1"}, "devDependencies": {"nodemon": "^3.1.10"}}