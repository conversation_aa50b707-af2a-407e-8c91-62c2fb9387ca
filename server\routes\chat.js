const express = require('express');
const ChatRoom = require('../models/Chat');
const Course = require('../models/Course');
const { auth, instructorAuth } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/chat/course/:courseId/rooms
// @desc    Get all chat rooms for a course
// @access  Private
router.get('/course/:courseId/rooms', auth, async (req, res) => {
  try {
    const course = await Course.findById(req.params.courseId);
    
    if (!course) {
      return res.status(404).json({
        message: 'Course not found'
      });
    }

    // Check if user has access to this course
    const isInstructor = course.instructor.toString() === req.user.id;
    const isEnrolled = course.enrolledStudents.some(
      enrollment => enrollment.student.toString() === req.user.id
    );
    const isAdmin = req.user.role === 'admin';

    if (!isInstructor && !isEnrolled && !isAdmin) {
      return res.status(403).json({
        message: 'Access denied'
      });
    }

    const chatRooms = await ChatRoom.find({ 
      course: req.params.courseId,
      isActive: true 
    })
    .populate('participants.user', 'firstName lastName avatar')
    .select('-messages'); // Don't include messages in room list

    res.json({
      success: true,
      chatRooms
    });
  } catch (error) {
    console.error('Get chat rooms error:', error);
    res.status(500).json({
      message: 'Server error getting chat rooms',
      error: error.message
    });
  }
});

// @route   POST /api/chat/course/:courseId/rooms
// @desc    Create a new chat room
// @access  Private/Instructor
router.post('/course/:courseId/rooms', auth, instructorAuth, async (req, res) => {
  try {
    const { name, description, roomType } = req.body;

    const course = await Course.findById(req.params.courseId);
    
    if (!course) {
      return res.status(404).json({
        message: 'Course not found'
      });
    }

    // Check if user is the instructor
    if (course.instructor.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({
        message: 'Access denied'
      });
    }

    const chatRoom = await ChatRoom.create({
      name,
      description,
      course: req.params.courseId,
      roomType: roomType || 'general',
      participants: [{
        user: req.user.id,
        role: 'admin'
      }]
    });

    await chatRoom.populate('participants.user', 'firstName lastName avatar');

    res.status(201).json({
      success: true,
      message: 'Chat room created successfully',
      chatRoom
    });
  } catch (error) {
    console.error('Create chat room error:', error);
    res.status(500).json({
      message: 'Server error creating chat room',
      error: error.message
    });
  }
});

// @route   GET /api/chat/rooms/:roomId
// @desc    Get chat room details with messages
// @access  Private
router.get('/rooms/:roomId', auth, async (req, res) => {
  try {
    const chatRoom = await ChatRoom.findById(req.params.roomId)
      .populate('course', 'title instructor')
      .populate('participants.user', 'firstName lastName avatar')
      .populate('messages.sender', 'firstName lastName avatar')
      .populate('messages.replyTo');

    if (!chatRoom) {
      return res.status(404).json({
        message: 'Chat room not found'
      });
    }

    // Check if user is a participant
    const isParticipant = chatRoom.participants.some(
      p => p.user._id.toString() === req.user.id
    );

    if (!isParticipant && req.user.role !== 'admin') {
      return res.status(403).json({
        message: 'Access denied'
      });
    }

    // Update user's last seen
    const participant = chatRoom.participants.find(
      p => p.user._id.toString() === req.user.id
    );
    
    if (participant) {
      participant.lastSeen = new Date();
      await chatRoom.save();
    }

    // Get recent messages (last 50)
    const messages = chatRoom.messages
      .sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt))
      .slice(-50);

    res.json({
      success: true,
      chatRoom: {
        ...chatRoom.toObject(),
        messages
      }
    });
  } catch (error) {
    console.error('Get chat room error:', error);
    res.status(500).json({
      message: 'Server error getting chat room',
      error: error.message
    });
  }
});

// @route   POST /api/chat/rooms/:roomId/join
// @desc    Join a chat room
// @access  Private
router.post('/rooms/:roomId/join', auth, async (req, res) => {
  try {
    const chatRoom = await ChatRoom.findById(req.params.roomId)
      .populate('course');

    if (!chatRoom) {
      return res.status(404).json({
        message: 'Chat room not found'
      });
    }

    // Check if user has access to the course
    const course = chatRoom.course;
    const isInstructor = course.instructor.toString() === req.user.id;
    const isEnrolled = course.enrolledStudents.some(
      enrollment => enrollment.student.toString() === req.user.id
    );
    const isAdmin = req.user.role === 'admin';

    if (!isInstructor && !isEnrolled && !isAdmin) {
      return res.status(403).json({
        message: 'Access denied. You must be enrolled in the course.'
      });
    }

    // Add user as participant if not already
    await chatRoom.addParticipant(req.user.id);

    res.json({
      success: true,
      message: 'Successfully joined chat room'
    });
  } catch (error) {
    console.error('Join chat room error:', error);
    res.status(500).json({
      message: 'Server error joining chat room',
      error: error.message
    });
  }
});

// @route   POST /api/chat/rooms/:roomId/messages
// @desc    Send a message to chat room
// @access  Private
router.post('/rooms/:roomId/messages', auth, async (req, res) => {
  try {
    const { content, messageType, replyTo } = req.body;

    const chatRoom = await ChatRoom.findById(req.params.roomId);

    if (!chatRoom) {
      return res.status(404).json({
        message: 'Chat room not found'
      });
    }

    // Check if user is a participant
    const isParticipant = chatRoom.participants.some(
      p => p.user.toString() === req.user.id
    );

    if (!isParticipant && req.user.role !== 'admin') {
      return res.status(403).json({
        message: 'Access denied. You must join the chat room first.'
      });
    }

    const messageData = {
      content,
      sender: req.user.id,
      messageType: messageType || 'text',
      replyTo: replyTo || null
    };

    await chatRoom.addMessage(messageData);

    // Get the newly added message with populated sender
    await chatRoom.populate('messages.sender', 'firstName lastName avatar');
    const newMessage = chatRoom.messages[chatRoom.messages.length - 1];

    res.status(201).json({
      success: true,
      message: 'Message sent successfully',
      messageData: newMessage
    });
  } catch (error) {
    console.error('Send message error:', error);
    res.status(500).json({
      message: 'Server error sending message',
      error: error.message
    });
  }
});

// @route   PUT /api/chat/messages/:messageId/react
// @desc    Add reaction to a message
// @access  Private
router.put('/messages/:messageId/react', auth, async (req, res) => {
  try {
    const { emoji } = req.body;
    const { messageId } = req.params;

    const chatRoom = await ChatRoom.findOne({
      'messages._id': messageId
    });

    if (!chatRoom) {
      return res.status(404).json({
        message: 'Message not found'
      });
    }

    const message = chatRoom.messages.id(messageId);
    
    // Check if user already reacted with this emoji
    const existingReaction = message.reactions.find(
      r => r.user.toString() === req.user.id && r.emoji === emoji
    );

    if (existingReaction) {
      // Remove reaction
      message.reactions.pull(existingReaction._id);
    } else {
      // Add reaction
      message.reactions.push({
        user: req.user.id,
        emoji
      });
    }

    await chatRoom.save();

    res.json({
      success: true,
      message: 'Reaction updated successfully'
    });
  } catch (error) {
    console.error('React to message error:', error);
    res.status(500).json({
      message: 'Server error updating reaction',
      error: error.message
    });
  }
});

module.exports = router;
