import React, { useState } from 'react';
import {
  Container,
  Typo<PERSON>,
  Box,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Chip,
  Rating,
  Button,
  Avatar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Tabs,
  Tab,
  Divider,
  Alert
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  CheckCircle as CheckIcon,
  Person as PersonIcon,
  Schedule as ScheduleIcon,
  Language as LanguageIcon,
  Star as StarIcon
} from '@mui/icons-material';
import { useParams } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import PaymentModal from '../../components/payment/PaymentModal';

const CourseDetail = () => {
  const { id } = useParams();
  const { isAuthenticated, user } = useAuth();
  const [tabValue, setTabValue] = useState(0);
  const [paymentModalOpen, setPaymentModalOpen] = useState(false);

  // Sample course data - in real app, fetch from API
  const course = {
    id: 1,
    title: "Complete Web Development Bootcamp",
    instructor: {
      name: "<PERSON>",
      avatar: "https://via.placeholder.com/100x100",
      bio: "Senior Full Stack Developer with 10+ years of experience"
    },
    rating: 4.8,
    students: 1250,
    price: 99.99,
    thumbnail: "https://via.placeholder.com/600x400",
    category: "Programming",
    level: "Beginner",
    language: "English",
    duration: "40 hours",
    description: "Learn web development from scratch with HTML, CSS, JavaScript, React, and Node.js. This comprehensive course will take you from beginner to job-ready developer.",
    whatYouWillLearn: [
      "Build responsive websites with HTML5 and CSS3",
      "Master JavaScript fundamentals and ES6+",
      "Create dynamic web applications with React",
      "Build backend APIs with Node.js and Express",
      "Work with databases using MongoDB",
      "Deploy applications to production"
    ],
    requirements: [
      "No programming experience required",
      "A computer with internet connection",
      "Willingness to learn and practice"
    ],
    lessons: [
      {
        id: 1,
        title: "Introduction to Web Development",
        duration: "15:30",
        isPreview: true
      },
      {
        id: 2,
        title: "HTML Fundamentals",
        duration: "45:20",
        isPreview: false
      },
      {
        id: 3,
        title: "CSS Styling and Layout",
        duration: "60:15",
        isPreview: false
      },
      {
        id: 4,
        title: "JavaScript Basics",
        duration: "75:45",
        isPreview: true
      },
      {
        id: 5,
        title: "DOM Manipulation",
        duration: "50:30",
        isPreview: false
      }
    ],
    reviews: [
      {
        id: 1,
        student: "Alice Johnson",
        rating: 5,
        comment: "Excellent course! Very well structured and easy to follow.",
        date: "2024-01-15"
      },
      {
        id: 2,
        student: "Bob Smith",
        rating: 4,
        comment: "Great content, but could use more practical exercises.",
        date: "2024-01-10"
      }
    ]
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleEnroll = () => {
    setPaymentModalOpen(true);
  };

  const handlePaymentSuccess = () => {
    // In real app, call API to enroll
    console.log('Payment successful, enrolling in course:', course.id);
    alert('Successfully enrolled in course!');
  };

  const TabPanel = ({ children, value, index }) => (
    <div hidden={value !== index}>
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Grid container spacing={4}>
        {/* Main Content */}
        <Grid item xs={12} md={8}>
          <CardMedia
            component="img"
            height="400"
            image={course.thumbnail}
            alt={course.title}
            sx={{ borderRadius: 2, mb: 3 }}
          />

          <Box sx={{ mb: 3 }}>
            <Box display="flex" gap={1} mb={2}>
              <Chip label={course.category} color="primary" />
              <Chip label={course.level} variant="outlined" />
            </Box>
            
            <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
              {course.title}
            </Typography>
            
            <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
              {course.description}
            </Typography>

            <Box display="flex" alignItems="center" gap={3} mb={2}>
              <Box display="flex" alignItems="center">
                <Rating value={course.rating} precision={0.1} size="small" readOnly />
                <Typography variant="body2" sx={{ ml: 1 }}>
                  {course.rating} ({course.students} students)
                </Typography>
              </Box>
              <Box display="flex" alignItems="center">
                <PersonIcon sx={{ mr: 0.5, fontSize: 20 }} />
                <Typography variant="body2">{course.instructor.name}</Typography>
              </Box>
              <Box display="flex" alignItems="center">
                <ScheduleIcon sx={{ mr: 0.5, fontSize: 20 }} />
                <Typography variant="body2">{course.duration}</Typography>
              </Box>
              <Box display="flex" alignItems="center">
                <LanguageIcon sx={{ mr: 0.5, fontSize: 20 }} />
                <Typography variant="body2">{course.language}</Typography>
              </Box>
            </Box>
          </Box>

          {/* Tabs */}
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs value={tabValue} onChange={handleTabChange}>
              <Tab label="Overview" />
              <Tab label="Curriculum" />
              <Tab label="Instructor" />
              <Tab label="Reviews" />
            </Tabs>
          </Box>

          {/* Overview Tab */}
          <TabPanel value={tabValue} index={0}>
            <Typography variant="h6" gutterBottom>
              What you'll learn
            </Typography>
            <List>
              {course.whatYouWillLearn.map((item, index) => (
                <ListItem key={index} sx={{ py: 0.5 }}>
                  <ListItemIcon>
                    <CheckIcon color="success" />
                  </ListItemIcon>
                  <ListItemText primary={item} />
                </ListItem>
              ))}
            </List>

            <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
              Requirements
            </Typography>
            <List>
              {course.requirements.map((item, index) => (
                <ListItem key={index} sx={{ py: 0.5 }}>
                  <ListItemText primary={`• ${item}`} />
                </ListItem>
              ))}
            </List>
          </TabPanel>

          {/* Curriculum Tab */}
          <TabPanel value={tabValue} index={1}>
            <Typography variant="h6" gutterBottom>
              Course Content
            </Typography>
            <List>
              {course.lessons.map((lesson, index) => (
                <ListItem key={lesson.id} sx={{ border: 1, borderColor: 'grey.200', borderRadius: 1, mb: 1 }}>
                  <ListItemIcon>
                    <PlayIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary={`${index + 1}. ${lesson.title}`}
                    secondary={lesson.duration}
                  />
                  {lesson.isPreview && (
                    <Chip label="Preview" size="small" color="primary" />
                  )}
                </ListItem>
              ))}
            </List>
          </TabPanel>

          {/* Instructor Tab */}
          <TabPanel value={tabValue} index={2}>
            <Box display="flex" alignItems="center" mb={2}>
              <Avatar src={course.instructor.avatar} sx={{ width: 80, height: 80, mr: 2 }} />
              <Box>
                <Typography variant="h6">{course.instructor.name}</Typography>
                <Typography color="text.secondary">{course.instructor.bio}</Typography>
              </Box>
            </Box>
          </TabPanel>

          {/* Reviews Tab */}
          <TabPanel value={tabValue} index={3}>
            <Typography variant="h6" gutterBottom>
              Student Reviews
            </Typography>
            {course.reviews.map((review) => (
              <Box key={review.id} sx={{ mb: 3, p: 2, border: 1, borderColor: 'grey.200', borderRadius: 1 }}>
                <Box display="flex" alignItems="center" mb={1}>
                  <Typography variant="subtitle2" sx={{ mr: 2 }}>{review.student}</Typography>
                  <Rating value={review.rating} size="small" readOnly />
                  <Typography variant="caption" sx={{ ml: 'auto' }}>{review.date}</Typography>
                </Box>
                <Typography variant="body2">{review.comment}</Typography>
              </Box>
            ))}
          </TabPanel>
        </Grid>

        {/* Sidebar */}
        <Grid item xs={12} md={4}>
          <Card sx={{ position: 'sticky', top: 20 }}>
            <CardContent>
              <Typography variant="h4" color="primary" fontWeight="bold" gutterBottom>
                ${course.price}
              </Typography>

              {isAuthenticated ? (
                <Button
                  fullWidth
                  variant="contained"
                  size="large"
                  onClick={handleEnroll}
                  sx={{ mb: 2 }}
                >
                  Enroll Now
                </Button>
              ) : (
                <Alert severity="info" sx={{ mb: 2 }}>
                  Please log in to enroll in this course
                </Alert>
              )}

              <Button
                fullWidth
                variant="outlined"
                size="large"
                sx={{ mb: 3 }}
              >
                Add to Wishlist
              </Button>

              <Divider sx={{ mb: 2 }} />

              <Typography variant="h6" gutterBottom>
                This course includes:
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemText primary={`${course.duration} on-demand video`} />
                </ListItem>
                <ListItem>
                  <ListItemText primary="Full lifetime access" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="Access on mobile and TV" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="Certificate of completion" />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Payment Modal */}
      <PaymentModal
        open={paymentModalOpen}
        onClose={() => setPaymentModalOpen(false)}
        course={course}
        onPaymentSuccess={handlePaymentSuccess}
      />
    </Container>
  );
};

export default CourseDetail;
