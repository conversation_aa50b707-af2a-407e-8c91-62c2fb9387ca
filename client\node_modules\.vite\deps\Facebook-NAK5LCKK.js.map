{"version": 3, "sources": ["../../react-player/lib/players/Facebook.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar Facebook_exports = {};\n__export(Facebook_exports, {\n  default: () => Facebook\n});\nmodule.exports = __toCommonJS(Facebook_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_utils = require(\"../utils\");\nvar import_patterns = require(\"../patterns\");\nconst SDK_URL = \"https://connect.facebook.net/en_US/sdk.js\";\nconst SDK_GLOBAL = \"FB\";\nconst SDK_GLOBAL_READY = \"fbAsyncInit\";\nconst PLAYER_ID_PREFIX = \"facebook-player-\";\nclass Facebook extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    __publicField(this, \"callPlayer\", import_utils.callPlayer);\n    __publicField(this, \"playerID\", this.props.config.playerId || `${PLAYER_ID_PREFIX}${(0, import_utils.randomString)()}`);\n    __publicField(this, \"mute\", () => {\n      this.callPlayer(\"mute\");\n    });\n    __publicField(this, \"unmute\", () => {\n      this.callPlayer(\"unmute\");\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n  }\n  load(url, isReady) {\n    if (isReady) {\n      (0, import_utils.getSDK)(SDK_URL, SDK_GLOBAL, SDK_GLOBAL_READY).then((FB) => FB.XFBML.parse());\n      return;\n    }\n    (0, import_utils.getSDK)(SDK_URL, SDK_GLOBAL, SDK_GLOBAL_READY).then((FB) => {\n      FB.init({\n        appId: this.props.config.appId,\n        xfbml: true,\n        version: this.props.config.version\n      });\n      FB.Event.subscribe(\"xfbml.render\", (msg) => {\n        this.props.onLoaded();\n      });\n      FB.Event.subscribe(\"xfbml.ready\", (msg) => {\n        if (msg.type === \"video\" && msg.id === this.playerID) {\n          this.player = msg.instance;\n          this.player.subscribe(\"startedPlaying\", this.props.onPlay);\n          this.player.subscribe(\"paused\", this.props.onPause);\n          this.player.subscribe(\"finishedPlaying\", this.props.onEnded);\n          this.player.subscribe(\"startedBuffering\", this.props.onBuffer);\n          this.player.subscribe(\"finishedBuffering\", this.props.onBufferEnd);\n          this.player.subscribe(\"error\", this.props.onError);\n          if (this.props.muted) {\n            this.callPlayer(\"mute\");\n          } else {\n            this.callPlayer(\"unmute\");\n          }\n          this.props.onReady();\n          document.getElementById(this.playerID).querySelector(\"iframe\").style.visibility = \"visible\";\n        }\n      });\n    });\n  }\n  play() {\n    this.callPlayer(\"play\");\n  }\n  pause() {\n    this.callPlayer(\"pause\");\n  }\n  stop() {\n  }\n  seekTo(seconds, keepPlaying = true) {\n    this.callPlayer(\"seek\", seconds);\n    if (!keepPlaying) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n    this.callPlayer(\"setVolume\", fraction);\n  }\n  getDuration() {\n    return this.callPlayer(\"getDuration\");\n  }\n  getCurrentTime() {\n    return this.callPlayer(\"getCurrentPosition\");\n  }\n  getSecondsLoaded() {\n    return null;\n  }\n  render() {\n    const { attributes } = this.props.config;\n    const style = {\n      width: \"100%\",\n      height: \"100%\"\n    };\n    return /* @__PURE__ */ import_react.default.createElement(\n      \"div\",\n      {\n        style,\n        id: this.playerID,\n        className: \"fb-video\",\n        \"data-href\": this.props.url,\n        \"data-autoplay\": this.props.playing ? \"true\" : \"false\",\n        \"data-allowfullscreen\": \"true\",\n        \"data-controls\": this.props.controls ? \"true\" : \"false\",\n        ...attributes\n      }\n    );\n  }\n}\n__publicField(Facebook, \"displayName\", \"Facebook\");\n__publicField(Facebook, \"canPlay\", import_patterns.canPlay.facebook);\n__publicField(Facebook, \"loopOnEnded\", true);\n"], "mappings": ";;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,WAAW,OAAO;AACtB,QAAI,YAAY,OAAO;AACvB,QAAI,mBAAmB,OAAO;AAC9B,QAAI,oBAAoB,OAAO;AAC/B,QAAI,eAAe,OAAO;AAC1B,QAAI,eAAe,OAAO,UAAU;AACpC,QAAI,kBAAkB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,UAAU,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC1J,QAAI,WAAW,CAAC,QAAQ,QAAQ;AAC9B,eAAS,QAAQ;AACf,kBAAU,QAAQ,MAAM,EAAE,KAAK,IAAI,IAAI,GAAG,YAAY,KAAK,CAAC;AAAA,IAChE;AACA,QAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,UAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,iBAAS,OAAO,kBAAkB,IAAI;AACpC,cAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,sBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,MACvH;AACA,aAAO;AAAA,IACT;AACA,QAAI,UAAU,CAAC,KAAK,YAAY,YAAY,SAAS,OAAO,OAAO,SAAS,aAAa,GAAG,CAAC,IAAI,CAAC,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA,MAKnG,cAAc,CAAC,OAAO,CAAC,IAAI,aAAa,UAAU,QAAQ,WAAW,EAAE,OAAO,KAAK,YAAY,KAAK,CAAC,IAAI;AAAA,MACzG;AAAA,IACF;AACA,QAAI,eAAe,CAAC,QAAQ,YAAY,UAAU,CAAC,GAAG,cAAc,EAAE,OAAO,KAAK,CAAC,GAAG,GAAG;AACzF,QAAI,gBAAgB,CAAC,KAAK,KAAK,UAAU;AACvC,sBAAgB,KAAK,OAAO,QAAQ,WAAW,MAAM,KAAK,KAAK,KAAK;AACpE,aAAO;AAAA,IACT;AACA,QAAI,mBAAmB,CAAC;AACxB,aAAS,kBAAkB;AAAA,MACzB,SAAS,MAAM;AAAA,IACjB,CAAC;AACD,WAAO,UAAU,aAAa,gBAAgB;AAC9C,QAAI,eAAe,QAAQ,eAAgB;AAC3C,QAAI,eAAe;AACnB,QAAI,kBAAkB;AACtB,QAAM,UAAU;AAChB,QAAM,aAAa;AACnB,QAAM,mBAAmB;AACzB,QAAM,mBAAmB;AACzB,QAAM,WAAN,cAAuB,aAAa,UAAU;AAAA,MAC5C,cAAc;AACZ,cAAM,GAAG,SAAS;AAClB,sBAAc,MAAM,cAAc,aAAa,UAAU;AACzD,sBAAc,MAAM,YAAY,KAAK,MAAM,OAAO,YAAY,GAAG,gBAAgB,IAAI,GAAG,aAAa,cAAc,CAAC,EAAE;AACtH,sBAAc,MAAM,QAAQ,MAAM;AAChC,eAAK,WAAW,MAAM;AAAA,QACxB,CAAC;AACD,sBAAc,MAAM,UAAU,MAAM;AAClC,eAAK,WAAW,QAAQ;AAAA,QAC1B,CAAC;AAAA,MACH;AAAA,MACA,oBAAoB;AAClB,aAAK,MAAM,WAAW,KAAK,MAAM,QAAQ,IAAI;AAAA,MAC/C;AAAA,MACA,KAAK,KAAK,SAAS;AACjB,YAAI,SAAS;AACX,WAAC,GAAG,aAAa,QAAQ,SAAS,YAAY,gBAAgB,EAAE,KAAK,CAAC,OAAO,GAAG,MAAM,MAAM,CAAC;AAC7F;AAAA,QACF;AACA,SAAC,GAAG,aAAa,QAAQ,SAAS,YAAY,gBAAgB,EAAE,KAAK,CAAC,OAAO;AAC3E,aAAG,KAAK;AAAA,YACN,OAAO,KAAK,MAAM,OAAO;AAAA,YACzB,OAAO;AAAA,YACP,SAAS,KAAK,MAAM,OAAO;AAAA,UAC7B,CAAC;AACD,aAAG,MAAM,UAAU,gBAAgB,CAAC,QAAQ;AAC1C,iBAAK,MAAM,SAAS;AAAA,UACtB,CAAC;AACD,aAAG,MAAM,UAAU,eAAe,CAAC,QAAQ;AACzC,gBAAI,IAAI,SAAS,WAAW,IAAI,OAAO,KAAK,UAAU;AACpD,mBAAK,SAAS,IAAI;AAClB,mBAAK,OAAO,UAAU,kBAAkB,KAAK,MAAM,MAAM;AACzD,mBAAK,OAAO,UAAU,UAAU,KAAK,MAAM,OAAO;AAClD,mBAAK,OAAO,UAAU,mBAAmB,KAAK,MAAM,OAAO;AAC3D,mBAAK,OAAO,UAAU,oBAAoB,KAAK,MAAM,QAAQ;AAC7D,mBAAK,OAAO,UAAU,qBAAqB,KAAK,MAAM,WAAW;AACjE,mBAAK,OAAO,UAAU,SAAS,KAAK,MAAM,OAAO;AACjD,kBAAI,KAAK,MAAM,OAAO;AACpB,qBAAK,WAAW,MAAM;AAAA,cACxB,OAAO;AACL,qBAAK,WAAW,QAAQ;AAAA,cAC1B;AACA,mBAAK,MAAM,QAAQ;AACnB,uBAAS,eAAe,KAAK,QAAQ,EAAE,cAAc,QAAQ,EAAE,MAAM,aAAa;AAAA,YACpF;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,MACA,OAAO;AACL,aAAK,WAAW,MAAM;AAAA,MACxB;AAAA,MACA,QAAQ;AACN,aAAK,WAAW,OAAO;AAAA,MACzB;AAAA,MACA,OAAO;AAAA,MACP;AAAA,MACA,OAAO,SAAS,cAAc,MAAM;AAClC,aAAK,WAAW,QAAQ,OAAO;AAC/B,YAAI,CAAC,aAAa;AAChB,eAAK,MAAM;AAAA,QACb;AAAA,MACF;AAAA,MACA,UAAU,UAAU;AAClB,aAAK,WAAW,aAAa,QAAQ;AAAA,MACvC;AAAA,MACA,cAAc;AACZ,eAAO,KAAK,WAAW,aAAa;AAAA,MACtC;AAAA,MACA,iBAAiB;AACf,eAAO,KAAK,WAAW,oBAAoB;AAAA,MAC7C;AAAA,MACA,mBAAmB;AACjB,eAAO;AAAA,MACT;AAAA,MACA,SAAS;AACP,cAAM,EAAE,WAAW,IAAI,KAAK,MAAM;AAClC,cAAM,QAAQ;AAAA,UACZ,OAAO;AAAA,UACP,QAAQ;AAAA,QACV;AACA,eAAuB,aAAa,QAAQ;AAAA,UAC1C;AAAA,UACA;AAAA,YACE;AAAA,YACA,IAAI,KAAK;AAAA,YACT,WAAW;AAAA,YACX,aAAa,KAAK,MAAM;AAAA,YACxB,iBAAiB,KAAK,MAAM,UAAU,SAAS;AAAA,YAC/C,wBAAwB;AAAA,YACxB,iBAAiB,KAAK,MAAM,WAAW,SAAS;AAAA,YAChD,GAAG;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,kBAAc,UAAU,eAAe,UAAU;AACjD,kBAAc,UAAU,WAAW,gBAAgB,QAAQ,QAAQ;AACnE,kBAAc,UAAU,eAAe,IAAI;AAAA;AAAA;", "names": []}