import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  Divider,
  Alert,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip
} from '@mui/material';
import {
  CreditCard as CreditCardIcon,
  Security as SecurityIcon,
  CheckCircle as CheckIcon
} from '@mui/icons-material';

const PaymentModal = ({ open, onClose, course, onPaymentSuccess }) => {
  const [paymentStep, setPaymentStep] = useState(1); // 1: Details, 2: Processing, 3: Success
  const [paymentData, setPaymentData] = useState({
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardholderName: '',
    billingAddress: {
      street: '',
      city: '',
      state: '',
      zipCode: '',
      country: 'US'
    }
  });
  const [processing, setProcessing] = useState(false);
  const [errors, setErrors] = useState({});

  const handleInputChange = (field, value) => {
    setPaymentData({
      ...paymentData,
      [field]: value
    });
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors({
        ...errors,
        [field]: ''
      });
    }
  };

  const handleAddressChange = (field, value) => {
    setPaymentData({
      ...paymentData,
      billingAddress: {
        ...paymentData.billingAddress,
        [field]: value
      }
    });
  };

  const validateForm = () => {
    const newErrors = {};

    if (!paymentData.cardNumber || paymentData.cardNumber.length < 16) {
      newErrors.cardNumber = 'Please enter a valid card number';
    }
    if (!paymentData.expiryDate || !/^\d{2}\/\d{2}$/.test(paymentData.expiryDate)) {
      newErrors.expiryDate = 'Please enter expiry date in MM/YY format';
    }
    if (!paymentData.cvv || paymentData.cvv.length < 3) {
      newErrors.cvv = 'Please enter a valid CVV';
    }
    if (!paymentData.cardholderName.trim()) {
      newErrors.cardholderName = 'Please enter cardholder name';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const formatCardNumber = (value) => {
    // Remove all non-digit characters
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    // Add spaces every 4 digits
    const matches = v.match(/\d{4,16}/g);
    const match = matches && matches[0] || '';
    const parts = [];
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }
    if (parts.length) {
      return parts.join(' ');
    } else {
      return v;
    }
  };

  const formatExpiryDate = (value) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    if (v.length >= 2) {
      return v.substring(0, 2) + '/' + v.substring(2, 4);
    }
    return v;
  };

  const handleCardNumberChange = (e) => {
    const formatted = formatCardNumber(e.target.value);
    if (formatted.length <= 19) { // 16 digits + 3 spaces
      handleInputChange('cardNumber', formatted);
    }
  };

  const handleExpiryChange = (e) => {
    const formatted = formatExpiryDate(e.target.value);
    if (formatted.length <= 5) {
      handleInputChange('expiryDate', formatted);
    }
  };

  const handleCvvChange = (e) => {
    const value = e.target.value.replace(/[^0-9]/g, '');
    if (value.length <= 4) {
      handleInputChange('cvv', value);
    }
  };

  const processPayment = async () => {
    if (!validateForm()) return;

    setProcessing(true);
    setPaymentStep(2);

    // Simulate payment processing
    setTimeout(() => {
      setProcessing(false);
      setPaymentStep(3);
      
      // Call success callback after another delay
      setTimeout(() => {
        onPaymentSuccess();
        handleClose();
      }, 2000);
    }, 3000);
  };

  const handleClose = () => {
    setPaymentStep(1);
    setPaymentData({
      cardNumber: '',
      expiryDate: '',
      cvv: '',
      cardholderName: '',
      billingAddress: {
        street: '',
        city: '',
        state: '',
        zipCode: '',
        country: 'US'
      }
    });
    setErrors({});
    setProcessing(false);
    onClose();
  };

  const renderPaymentForm = () => (
    <Box>
      <Alert severity="info" sx={{ mb: 3 }}>
        <Box display="flex" alignItems="center">
          <SecurityIcon sx={{ mr: 1 }} />
          Your payment information is secure and encrypted
        </Box>
      </Alert>

      <Grid container spacing={3}>
        <Grid item xs={12}>
          <TextField
            fullWidth
            label="Card Number"
            value={paymentData.cardNumber}
            onChange={handleCardNumberChange}
            error={!!errors.cardNumber}
            helperText={errors.cardNumber}
            placeholder="1234 5678 9012 3456"
            InputProps={{
              startAdornment: <CreditCardIcon sx={{ mr: 1, color: 'text.secondary' }} />
            }}
          />
        </Grid>
        
        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            label="Expiry Date"
            value={paymentData.expiryDate}
            onChange={handleExpiryChange}
            error={!!errors.expiryDate}
            helperText={errors.expiryDate}
            placeholder="MM/YY"
          />
        </Grid>
        
        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            label="CVV"
            value={paymentData.cvv}
            onChange={handleCvvChange}
            error={!!errors.cvv}
            helperText={errors.cvv}
            placeholder="123"
          />
        </Grid>
        
        <Grid item xs={12}>
          <TextField
            fullWidth
            label="Cardholder Name"
            value={paymentData.cardholderName}
            onChange={(e) => handleInputChange('cardholderName', e.target.value)}
            error={!!errors.cardholderName}
            helperText={errors.cardholderName}
            placeholder="John Doe"
          />
        </Grid>

        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
            Billing Address
          </Typography>
        </Grid>

        <Grid item xs={12}>
          <TextField
            fullWidth
            label="Street Address"
            value={paymentData.billingAddress.street}
            onChange={(e) => handleAddressChange('street', e.target.value)}
          />
        </Grid>
        
        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            label="City"
            value={paymentData.billingAddress.city}
            onChange={(e) => handleAddressChange('city', e.target.value)}
          />
        </Grid>
        
        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            label="State"
            value={paymentData.billingAddress.state}
            onChange={(e) => handleAddressChange('state', e.target.value)}
          />
        </Grid>
        
        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            label="ZIP Code"
            value={paymentData.billingAddress.zipCode}
            onChange={(e) => handleAddressChange('zipCode', e.target.value)}
          />
        </Grid>
        
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <InputLabel>Country</InputLabel>
            <Select
              value={paymentData.billingAddress.country}
              onChange={(e) => handleAddressChange('country', e.target.value)}
              label="Country"
            >
              <MenuItem value="US">United States</MenuItem>
              <MenuItem value="CA">Canada</MenuItem>
              <MenuItem value="UK">United Kingdom</MenuItem>
              <MenuItem value="AU">Australia</MenuItem>
            </Select>
          </FormControl>
        </Grid>
      </Grid>
    </Box>
  );

  const renderProcessing = () => (
    <Box textAlign="center" py={4}>
      <CircularProgress size={60} sx={{ mb: 2 }} />
      <Typography variant="h6" gutterBottom>
        Processing Payment...
      </Typography>
      <Typography color="text.secondary">
        Please don't close this window
      </Typography>
    </Box>
  );

  const renderSuccess = () => (
    <Box textAlign="center" py={4}>
      <CheckIcon sx={{ fontSize: 80, color: 'success.main', mb: 2 }} />
      <Typography variant="h5" gutterBottom>
        Payment Successful!
      </Typography>
      <Typography color="text.secondary" sx={{ mb: 2 }}>
        You now have access to the course
      </Typography>
      <Chip label="Enrollment Confirmed" color="success" />
    </Box>
  );

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>
        {paymentStep === 1 && 'Complete Your Purchase'}
        {paymentStep === 2 && 'Processing Payment'}
        {paymentStep === 3 && 'Payment Complete'}
      </DialogTitle>
      
      <DialogContent>
        {paymentStep === 1 && (
          <Box>
            {/* Course Summary */}
            <Card sx={{ mb: 3, bgcolor: 'grey.50' }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  {course?.title}
                </Typography>
                <Typography color="text.secondary" gutterBottom>
                  by {course?.instructor}
                </Typography>
                <Divider sx={{ my: 2 }} />
                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Typography variant="h6">
                    Total Amount:
                  </Typography>
                  <Typography variant="h5" color="primary" fontWeight="bold">
                    ${course?.price}
                  </Typography>
                </Box>
              </CardContent>
            </Card>
            
            {renderPaymentForm()}
          </Box>
        )}
        
        {paymentStep === 2 && renderProcessing()}
        {paymentStep === 3 && renderSuccess()}
      </DialogContent>
      
      <DialogActions>
        {paymentStep === 1 && (
          <>
            <Button onClick={handleClose}>
              Cancel
            </Button>
            <Button
              variant="contained"
              onClick={processPayment}
              disabled={processing}
              size="large"
            >
              Pay ${course?.price}
            </Button>
          </>
        )}
        
        {paymentStep === 3 && (
          <Button onClick={handleClose} variant="contained">
            Continue to Course
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default PaymentModal;
