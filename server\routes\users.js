const express = require('express');
const User = require('../models/User');
const Course = require('../models/Course');
const Progress = require('../models/Progress');
const { auth, adminAuth, instructorAuth } = require('../middleware/auth');
const { uploadImage } = require('../config/cloudinary');

const router = express.Router();

// @route   GET /api/users
// @desc    Get all users (Admin only)
// @access  Private/Admin
router.get('/', auth, adminAuth, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    const search = req.query.search || '';
    const role = req.query.role || '';

    // Build query
    let query = {};
    if (search) {
      query.$or = [
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }
    if (role) {
      query.role = role;
    }

    const users = await User.find(query)
      .select('-password')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await User.countDocuments(query);

    res.json({
      success: true,
      users,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({
      message: 'Server error getting users',
      error: error.message
    });
  }
});

// @route   GET /api/users/:id
// @desc    Get user by ID
// @access  Private
router.get('/:id', auth, async (req, res) => {
  try {
    const user = await User.findById(req.params.id)
      .select('-password')
      .populate('enrolledCourses', 'title thumbnail instructor rating')
      .populate('createdCourses', 'title thumbnail enrolledStudents rating');

    if (!user) {
      return res.status(404).json({
        message: 'User not found'
      });
    }

    // Check if user can view this profile
    if (req.user.id !== user._id.toString() && req.user.role !== 'admin') {
      return res.status(403).json({
        message: 'Access denied'
      });
    }

    res.json({
      success: true,
      user
    });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({
      message: 'Server error getting user',
      error: error.message
    });
  }
});

// @route   PUT /api/users/:id
// @desc    Update user (Admin only)
// @access  Private/Admin
router.put('/:id', auth, adminAuth, async (req, res) => {
  try {
    const { firstName, lastName, email, role, isActive } = req.body;

    const user = await User.findById(req.params.id);
    if (!user) {
      return res.status(404).json({
        message: 'User not found'
      });
    }

    // Check if email is already taken by another user
    if (email && email !== user.email) {
      const existingUser = await User.findOne({ email });
      if (existingUser) {
        return res.status(400).json({
          message: 'Email is already taken'
        });
      }
    }

    // Update fields
    if (firstName) user.firstName = firstName;
    if (lastName) user.lastName = lastName;
    if (email) user.email = email;
    if (role) user.role = role;
    if (isActive !== undefined) user.isActive = isActive;

    await user.save();

    res.json({
      success: true,
      message: 'User updated successfully',
      user: {
        id: user._id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        role: user.role,
        isActive: user.isActive
      }
    });
  } catch (error) {
    console.error('Update user error:', error);
    res.status(500).json({
      message: 'Server error updating user',
      error: error.message
    });
  }
});

// @route   DELETE /api/users/:id
// @desc    Delete user (Admin only)
// @access  Private/Admin
router.delete('/:id', auth, adminAuth, async (req, res) => {
  try {
    const user = await User.findById(req.params.id);
    if (!user) {
      return res.status(404).json({
        message: 'User not found'
      });
    }

    // Don't allow deleting admin users
    if (user.role === 'admin') {
      return res.status(400).json({
        message: 'Cannot delete admin users'
      });
    }

    await User.findByIdAndDelete(req.params.id);

    res.json({
      success: true,
      message: 'User deleted successfully'
    });
  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({
      message: 'Server error deleting user',
      error: error.message
    });
  }
});

// @route   POST /api/users/upload-avatar
// @desc    Upload user avatar
// @access  Private
router.post('/upload-avatar', auth, async (req, res) => {
  try {
    if (!req.files || !req.files.avatar) {
      return res.status(400).json({
        message: 'Please upload an image file'
      });
    }

    const file = req.files.avatar;

    // Check file type
    if (!file.mimetype.startsWith('image/')) {
      return res.status(400).json({
        message: 'Please upload an image file'
      });
    }

    // Check file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      return res.status(400).json({
        message: 'Image size should be less than 5MB'
      });
    }

    // Upload to Cloudinary
    const result = await uploadImage(file.tempFilePath, {
      folder: 'lms/avatars',
      width: 200,
      height: 200,
      crop: 'fill'
    });

    // Update user avatar
    const user = await User.findById(req.user.id);
    user.avatar = result.secure_url;
    await user.save();

    res.json({
      success: true,
      message: 'Avatar uploaded successfully',
      avatar: result.secure_url
    });
  } catch (error) {
    console.error('Avatar upload error:', error);
    res.status(500).json({
      message: 'Server error uploading avatar',
      error: error.message
    });
  }
});

// @route   GET /api/users/:id/dashboard
// @desc    Get user dashboard data
// @access  Private
router.get('/:id/dashboard', auth, async (req, res) => {
  try {
    const userId = req.params.id;

    // Check if user can view this dashboard
    if (req.user.id !== userId && req.user.role !== 'admin') {
      return res.status(403).json({
        message: 'Access denied'
      });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        message: 'User not found'
      });
    }

    let dashboardData = {};

    if (user.role === 'student') {
      // Student dashboard
      const enrolledCourses = await Course.find({
        'enrolledStudents.student': userId
      }).populate('instructor', 'firstName lastName');

      const progress = await Progress.find({ student: userId })
        .populate('course', 'title thumbnail');

      dashboardData = {
        enrolledCourses: enrolledCourses.length,
        completedCourses: progress.filter(p => p.completionPercentage === 100).length,
        inProgressCourses: progress.filter(p => p.completionPercentage > 0 && p.completionPercentage < 100).length,
        totalTimeSpent: progress.reduce((total, p) => total + p.totalTimeSpent, 0),
        recentCourses: enrolledCourses.slice(0, 5),
        recentProgress: progress.slice(0, 5)
      };
    } else if (user.role === 'instructor') {
      // Instructor dashboard
      const createdCourses = await Course.find({ instructor: userId });
      const totalStudents = createdCourses.reduce((total, course) => 
        total + course.enrolledStudents.length, 0);

      dashboardData = {
        totalCourses: createdCourses.length,
        publishedCourses: createdCourses.filter(c => c.isPublished).length,
        totalStudents,
        totalRevenue: createdCourses.reduce((total, course) => 
          total + (course.price * course.enrolledStudents.length), 0),
        recentCourses: createdCourses.slice(0, 5)
      };
    }

    res.json({
      success: true,
      dashboardData
    });
  } catch (error) {
    console.error('Dashboard error:', error);
    res.status(500).json({
      message: 'Server error getting dashboard data',
      error: error.message
    });
  }
});

module.exports = router;
