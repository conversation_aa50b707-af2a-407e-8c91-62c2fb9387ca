{"version": 3, "sources": ["../../react-player/lib/players/Mixcloud.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar Mixcloud_exports = {};\n__export(Mixcloud_exports, {\n  default: () => Mixcloud\n});\nmodule.exports = __toCommonJS(Mixcloud_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_utils = require(\"../utils\");\nvar import_patterns = require(\"../patterns\");\nconst SDK_URL = \"https://widget.mixcloud.com/media/js/widgetApi.js\";\nconst SDK_GLOBAL = \"Mixcloud\";\nclass Mixcloud extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    __publicField(this, \"callPlayer\", import_utils.callPlayer);\n    __publicField(this, \"duration\", null);\n    __publicField(this, \"currentTime\", null);\n    __publicField(this, \"secondsLoaded\", null);\n    __publicField(this, \"mute\", () => {\n    });\n    __publicField(this, \"unmute\", () => {\n    });\n    __publicField(this, \"ref\", (iframe) => {\n      this.iframe = iframe;\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n  }\n  load(url) {\n    (0, import_utils.getSDK)(SDK_URL, SDK_GLOBAL).then((Mixcloud2) => {\n      this.player = Mixcloud2.PlayerWidget(this.iframe);\n      this.player.ready.then(() => {\n        this.player.events.play.on(this.props.onPlay);\n        this.player.events.pause.on(this.props.onPause);\n        this.player.events.ended.on(this.props.onEnded);\n        this.player.events.error.on(this.props.error);\n        this.player.events.progress.on((seconds, duration) => {\n          this.currentTime = seconds;\n          this.duration = duration;\n        });\n        this.props.onReady();\n      });\n    }, this.props.onError);\n  }\n  play() {\n    this.callPlayer(\"play\");\n  }\n  pause() {\n    this.callPlayer(\"pause\");\n  }\n  stop() {\n  }\n  seekTo(seconds, keepPlaying = true) {\n    this.callPlayer(\"seek\", seconds);\n    if (!keepPlaying) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n  }\n  getDuration() {\n    return this.duration;\n  }\n  getCurrentTime() {\n    return this.currentTime;\n  }\n  getSecondsLoaded() {\n    return null;\n  }\n  render() {\n    const { url, config } = this.props;\n    const id = url.match(import_patterns.MATCH_URL_MIXCLOUD)[1];\n    const style = {\n      width: \"100%\",\n      height: \"100%\"\n    };\n    const query = (0, import_utils.queryString)({\n      ...config.options,\n      feed: `/${id}/`\n    });\n    return /* @__PURE__ */ import_react.default.createElement(\n      \"iframe\",\n      {\n        key: id,\n        ref: this.ref,\n        style,\n        src: `https://www.mixcloud.com/widget/iframe/?${query}`,\n        frameBorder: \"0\",\n        allow: \"autoplay\"\n      }\n    );\n  }\n}\n__publicField(Mixcloud, \"displayName\", \"Mixcloud\");\n__publicField(Mixcloud, \"canPlay\", import_patterns.canPlay.mixcloud);\n__publicField(Mixcloud, \"loopOnEnded\", true);\n"], "mappings": ";;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,WAAW,OAAO;AACtB,QAAI,YAAY,OAAO;AACvB,QAAI,mBAAmB,OAAO;AAC9B,QAAI,oBAAoB,OAAO;AAC/B,QAAI,eAAe,OAAO;AAC1B,QAAI,eAAe,OAAO,UAAU;AACpC,QAAI,kBAAkB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,UAAU,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC1J,QAAI,WAAW,CAAC,QAAQ,QAAQ;AAC9B,eAAS,QAAQ;AACf,kBAAU,QAAQ,MAAM,EAAE,KAAK,IAAI,IAAI,GAAG,YAAY,KAAK,CAAC;AAAA,IAChE;AACA,QAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,UAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,iBAAS,OAAO,kBAAkB,IAAI;AACpC,cAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,sBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,MACvH;AACA,aAAO;AAAA,IACT;AACA,QAAI,UAAU,CAAC,KAAK,YAAY,YAAY,SAAS,OAAO,OAAO,SAAS,aAAa,GAAG,CAAC,IAAI,CAAC,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA,MAKnG,cAAc,CAAC,OAAO,CAAC,IAAI,aAAa,UAAU,QAAQ,WAAW,EAAE,OAAO,KAAK,YAAY,KAAK,CAAC,IAAI;AAAA,MACzG;AAAA,IACF;AACA,QAAI,eAAe,CAAC,QAAQ,YAAY,UAAU,CAAC,GAAG,cAAc,EAAE,OAAO,KAAK,CAAC,GAAG,GAAG;AACzF,QAAI,gBAAgB,CAAC,KAAK,KAAK,UAAU;AACvC,sBAAgB,KAAK,OAAO,QAAQ,WAAW,MAAM,KAAK,KAAK,KAAK;AACpE,aAAO;AAAA,IACT;AACA,QAAI,mBAAmB,CAAC;AACxB,aAAS,kBAAkB;AAAA,MACzB,SAAS,MAAM;AAAA,IACjB,CAAC;AACD,WAAO,UAAU,aAAa,gBAAgB;AAC9C,QAAI,eAAe,QAAQ,eAAgB;AAC3C,QAAI,eAAe;AACnB,QAAI,kBAAkB;AACtB,QAAM,UAAU;AAChB,QAAM,aAAa;AACnB,QAAM,WAAN,cAAuB,aAAa,UAAU;AAAA,MAC5C,cAAc;AACZ,cAAM,GAAG,SAAS;AAClB,sBAAc,MAAM,cAAc,aAAa,UAAU;AACzD,sBAAc,MAAM,YAAY,IAAI;AACpC,sBAAc,MAAM,eAAe,IAAI;AACvC,sBAAc,MAAM,iBAAiB,IAAI;AACzC,sBAAc,MAAM,QAAQ,MAAM;AAAA,QAClC,CAAC;AACD,sBAAc,MAAM,UAAU,MAAM;AAAA,QACpC,CAAC;AACD,sBAAc,MAAM,OAAO,CAAC,WAAW;AACrC,eAAK,SAAS;AAAA,QAChB,CAAC;AAAA,MACH;AAAA,MACA,oBAAoB;AAClB,aAAK,MAAM,WAAW,KAAK,MAAM,QAAQ,IAAI;AAAA,MAC/C;AAAA,MACA,KAAK,KAAK;AACR,SAAC,GAAG,aAAa,QAAQ,SAAS,UAAU,EAAE,KAAK,CAAC,cAAc;AAChE,eAAK,SAAS,UAAU,aAAa,KAAK,MAAM;AAChD,eAAK,OAAO,MAAM,KAAK,MAAM;AAC3B,iBAAK,OAAO,OAAO,KAAK,GAAG,KAAK,MAAM,MAAM;AAC5C,iBAAK,OAAO,OAAO,MAAM,GAAG,KAAK,MAAM,OAAO;AAC9C,iBAAK,OAAO,OAAO,MAAM,GAAG,KAAK,MAAM,OAAO;AAC9C,iBAAK,OAAO,OAAO,MAAM,GAAG,KAAK,MAAM,KAAK;AAC5C,iBAAK,OAAO,OAAO,SAAS,GAAG,CAAC,SAAS,aAAa;AACpD,mBAAK,cAAc;AACnB,mBAAK,WAAW;AAAA,YAClB,CAAC;AACD,iBAAK,MAAM,QAAQ;AAAA,UACrB,CAAC;AAAA,QACH,GAAG,KAAK,MAAM,OAAO;AAAA,MACvB;AAAA,MACA,OAAO;AACL,aAAK,WAAW,MAAM;AAAA,MACxB;AAAA,MACA,QAAQ;AACN,aAAK,WAAW,OAAO;AAAA,MACzB;AAAA,MACA,OAAO;AAAA,MACP;AAAA,MACA,OAAO,SAAS,cAAc,MAAM;AAClC,aAAK,WAAW,QAAQ,OAAO;AAC/B,YAAI,CAAC,aAAa;AAChB,eAAK,MAAM;AAAA,QACb;AAAA,MACF;AAAA,MACA,UAAU,UAAU;AAAA,MACpB;AAAA,MACA,cAAc;AACZ,eAAO,KAAK;AAAA,MACd;AAAA,MACA,iBAAiB;AACf,eAAO,KAAK;AAAA,MACd;AAAA,MACA,mBAAmB;AACjB,eAAO;AAAA,MACT;AAAA,MACA,SAAS;AACP,cAAM,EAAE,KAAK,OAAO,IAAI,KAAK;AAC7B,cAAM,KAAK,IAAI,MAAM,gBAAgB,kBAAkB,EAAE,CAAC;AAC1D,cAAM,QAAQ;AAAA,UACZ,OAAO;AAAA,UACP,QAAQ;AAAA,QACV;AACA,cAAM,SAAS,GAAG,aAAa,aAAa;AAAA,UAC1C,GAAG,OAAO;AAAA,UACV,MAAM,IAAI,EAAE;AAAA,QACd,CAAC;AACD,eAAuB,aAAa,QAAQ;AAAA,UAC1C;AAAA,UACA;AAAA,YACE,KAAK;AAAA,YACL,KAAK,KAAK;AAAA,YACV;AAAA,YACA,KAAK,2CAA2C,KAAK;AAAA,YACrD,aAAa;AAAA,YACb,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,kBAAc,UAAU,eAAe,UAAU;AACjD,kBAAc,UAAU,WAAW,gBAAgB,QAAQ,QAAQ;AACnE,kBAAc,UAAU,eAAe,IAAI;AAAA;AAAA;", "names": []}