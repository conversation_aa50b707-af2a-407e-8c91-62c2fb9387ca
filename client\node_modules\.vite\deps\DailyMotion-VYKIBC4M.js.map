{"version": 3, "sources": ["../../react-player/lib/players/DailyMotion.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar DailyMotion_exports = {};\n__export(DailyMotion_exports, {\n  default: () => DailyMotion\n});\nmodule.exports = __toCommonJS(DailyMotion_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_utils = require(\"../utils\");\nvar import_patterns = require(\"../patterns\");\nconst SDK_URL = \"https://api.dmcdn.net/all.js\";\nconst SDK_GLOBAL = \"DM\";\nconst SDK_GLOBAL_READY = \"dmAsyncInit\";\nclass DailyMotion extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    __publicField(this, \"callPlayer\", import_utils.callPlayer);\n    __publicField(this, \"onDurationChange\", () => {\n      const duration = this.getDuration();\n      this.props.onDuration(duration);\n    });\n    __publicField(this, \"mute\", () => {\n      this.callPlayer(\"setMuted\", true);\n    });\n    __publicField(this, \"unmute\", () => {\n      this.callPlayer(\"setMuted\", false);\n    });\n    __publicField(this, \"ref\", (container) => {\n      this.container = container;\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n  }\n  load(url) {\n    const { controls, config, onError, playing } = this.props;\n    const [, id] = url.match(import_patterns.MATCH_URL_DAILYMOTION);\n    if (this.player) {\n      this.player.load(id, {\n        start: (0, import_utils.parseStartTime)(url),\n        autoplay: playing\n      });\n      return;\n    }\n    (0, import_utils.getSDK)(SDK_URL, SDK_GLOBAL, SDK_GLOBAL_READY, (DM) => DM.player).then((DM) => {\n      if (!this.container)\n        return;\n      const Player = DM.player;\n      this.player = new Player(this.container, {\n        width: \"100%\",\n        height: \"100%\",\n        video: id,\n        params: {\n          controls,\n          autoplay: this.props.playing,\n          mute: this.props.muted,\n          start: (0, import_utils.parseStartTime)(url),\n          origin: window.location.origin,\n          ...config.params\n        },\n        events: {\n          apiready: this.props.onReady,\n          seeked: () => this.props.onSeek(this.player.currentTime),\n          video_end: this.props.onEnded,\n          durationchange: this.onDurationChange,\n          pause: this.props.onPause,\n          playing: this.props.onPlay,\n          waiting: this.props.onBuffer,\n          error: (event) => onError(event)\n        }\n      });\n    }, onError);\n  }\n  play() {\n    this.callPlayer(\"play\");\n  }\n  pause() {\n    this.callPlayer(\"pause\");\n  }\n  stop() {\n  }\n  seekTo(seconds, keepPlaying = true) {\n    this.callPlayer(\"seek\", seconds);\n    if (!keepPlaying) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n    this.callPlayer(\"setVolume\", fraction);\n  }\n  getDuration() {\n    return this.player.duration || null;\n  }\n  getCurrentTime() {\n    return this.player.currentTime;\n  }\n  getSecondsLoaded() {\n    return this.player.bufferedTime;\n  }\n  render() {\n    const { display } = this.props;\n    const style = {\n      width: \"100%\",\n      height: \"100%\",\n      display\n    };\n    return /* @__PURE__ */ import_react.default.createElement(\"div\", { style }, /* @__PURE__ */ import_react.default.createElement(\"div\", { ref: this.ref }));\n  }\n}\n__publicField(DailyMotion, \"displayName\", \"DailyMotion\");\n__publicField(DailyMotion, \"canPlay\", import_patterns.canPlay.dailymotion);\n__publicField(DailyMotion, \"loopOnEnded\", true);\n"], "mappings": ";;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,WAAW,OAAO;AACtB,QAAI,YAAY,OAAO;AACvB,QAAI,mBAAmB,OAAO;AAC9B,QAAI,oBAAoB,OAAO;AAC/B,QAAI,eAAe,OAAO;AAC1B,QAAI,eAAe,OAAO,UAAU;AACpC,QAAI,kBAAkB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,UAAU,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC1J,QAAI,WAAW,CAAC,QAAQ,QAAQ;AAC9B,eAAS,QAAQ;AACf,kBAAU,QAAQ,MAAM,EAAE,KAAK,IAAI,IAAI,GAAG,YAAY,KAAK,CAAC;AAAA,IAChE;AACA,QAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,UAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,iBAAS,OAAO,kBAAkB,IAAI;AACpC,cAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,sBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,MACvH;AACA,aAAO;AAAA,IACT;AACA,QAAI,UAAU,CAAC,KAAK,YAAY,YAAY,SAAS,OAAO,OAAO,SAAS,aAAa,GAAG,CAAC,IAAI,CAAC,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA,MAKnG,cAAc,CAAC,OAAO,CAAC,IAAI,aAAa,UAAU,QAAQ,WAAW,EAAE,OAAO,KAAK,YAAY,KAAK,CAAC,IAAI;AAAA,MACzG;AAAA,IACF;AACA,QAAI,eAAe,CAAC,QAAQ,YAAY,UAAU,CAAC,GAAG,cAAc,EAAE,OAAO,KAAK,CAAC,GAAG,GAAG;AACzF,QAAI,gBAAgB,CAAC,KAAK,KAAK,UAAU;AACvC,sBAAgB,KAAK,OAAO,QAAQ,WAAW,MAAM,KAAK,KAAK,KAAK;AACpE,aAAO;AAAA,IACT;AACA,QAAI,sBAAsB,CAAC;AAC3B,aAAS,qBAAqB;AAAA,MAC5B,SAAS,MAAM;AAAA,IACjB,CAAC;AACD,WAAO,UAAU,aAAa,mBAAmB;AACjD,QAAI,eAAe,QAAQ,eAAgB;AAC3C,QAAI,eAAe;AACnB,QAAI,kBAAkB;AACtB,QAAM,UAAU;AAChB,QAAM,aAAa;AACnB,QAAM,mBAAmB;AACzB,QAAM,cAAN,cAA0B,aAAa,UAAU;AAAA,MAC/C,cAAc;AACZ,cAAM,GAAG,SAAS;AAClB,sBAAc,MAAM,cAAc,aAAa,UAAU;AACzD,sBAAc,MAAM,oBAAoB,MAAM;AAC5C,gBAAM,WAAW,KAAK,YAAY;AAClC,eAAK,MAAM,WAAW,QAAQ;AAAA,QAChC,CAAC;AACD,sBAAc,MAAM,QAAQ,MAAM;AAChC,eAAK,WAAW,YAAY,IAAI;AAAA,QAClC,CAAC;AACD,sBAAc,MAAM,UAAU,MAAM;AAClC,eAAK,WAAW,YAAY,KAAK;AAAA,QACnC,CAAC;AACD,sBAAc,MAAM,OAAO,CAAC,cAAc;AACxC,eAAK,YAAY;AAAA,QACnB,CAAC;AAAA,MACH;AAAA,MACA,oBAAoB;AAClB,aAAK,MAAM,WAAW,KAAK,MAAM,QAAQ,IAAI;AAAA,MAC/C;AAAA,MACA,KAAK,KAAK;AACR,cAAM,EAAE,UAAU,QAAQ,SAAS,QAAQ,IAAI,KAAK;AACpD,cAAM,CAAC,EAAE,EAAE,IAAI,IAAI,MAAM,gBAAgB,qBAAqB;AAC9D,YAAI,KAAK,QAAQ;AACf,eAAK,OAAO,KAAK,IAAI;AAAA,YACnB,QAAQ,GAAG,aAAa,gBAAgB,GAAG;AAAA,YAC3C,UAAU;AAAA,UACZ,CAAC;AACD;AAAA,QACF;AACA,SAAC,GAAG,aAAa,QAAQ,SAAS,YAAY,kBAAkB,CAAC,OAAO,GAAG,MAAM,EAAE,KAAK,CAAC,OAAO;AAC9F,cAAI,CAAC,KAAK;AACR;AACF,gBAAM,SAAS,GAAG;AAClB,eAAK,SAAS,IAAI,OAAO,KAAK,WAAW;AAAA,YACvC,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,OAAO;AAAA,YACP,QAAQ;AAAA,cACN;AAAA,cACA,UAAU,KAAK,MAAM;AAAA,cACrB,MAAM,KAAK,MAAM;AAAA,cACjB,QAAQ,GAAG,aAAa,gBAAgB,GAAG;AAAA,cAC3C,QAAQ,OAAO,SAAS;AAAA,cACxB,GAAG,OAAO;AAAA,YACZ;AAAA,YACA,QAAQ;AAAA,cACN,UAAU,KAAK,MAAM;AAAA,cACrB,QAAQ,MAAM,KAAK,MAAM,OAAO,KAAK,OAAO,WAAW;AAAA,cACvD,WAAW,KAAK,MAAM;AAAA,cACtB,gBAAgB,KAAK;AAAA,cACrB,OAAO,KAAK,MAAM;AAAA,cAClB,SAAS,KAAK,MAAM;AAAA,cACpB,SAAS,KAAK,MAAM;AAAA,cACpB,OAAO,CAAC,UAAU,QAAQ,KAAK;AAAA,YACjC;AAAA,UACF,CAAC;AAAA,QACH,GAAG,OAAO;AAAA,MACZ;AAAA,MACA,OAAO;AACL,aAAK,WAAW,MAAM;AAAA,MACxB;AAAA,MACA,QAAQ;AACN,aAAK,WAAW,OAAO;AAAA,MACzB;AAAA,MACA,OAAO;AAAA,MACP;AAAA,MACA,OAAO,SAAS,cAAc,MAAM;AAClC,aAAK,WAAW,QAAQ,OAAO;AAC/B,YAAI,CAAC,aAAa;AAChB,eAAK,MAAM;AAAA,QACb;AAAA,MACF;AAAA,MACA,UAAU,UAAU;AAClB,aAAK,WAAW,aAAa,QAAQ;AAAA,MACvC;AAAA,MACA,cAAc;AACZ,eAAO,KAAK,OAAO,YAAY;AAAA,MACjC;AAAA,MACA,iBAAiB;AACf,eAAO,KAAK,OAAO;AAAA,MACrB;AAAA,MACA,mBAAmB;AACjB,eAAO,KAAK,OAAO;AAAA,MACrB;AAAA,MACA,SAAS;AACP,cAAM,EAAE,QAAQ,IAAI,KAAK;AACzB,cAAM,QAAQ;AAAA,UACZ,OAAO;AAAA,UACP,QAAQ;AAAA,UACR;AAAA,QACF;AACA,eAAuB,aAAa,QAAQ,cAAc,OAAO,EAAE,MAAM,GAAmB,aAAa,QAAQ,cAAc,OAAO,EAAE,KAAK,KAAK,IAAI,CAAC,CAAC;AAAA,MAC1J;AAAA,IACF;AACA,kBAAc,aAAa,eAAe,aAAa;AACvD,kBAAc,aAAa,WAAW,gBAAgB,QAAQ,WAAW;AACzE,kBAAc,aAAa,eAAe,IAAI;AAAA;AAAA;", "names": []}