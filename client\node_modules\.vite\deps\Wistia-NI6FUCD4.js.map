{"version": 3, "sources": ["../../react-player/lib/players/Wistia.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar Wistia_exports = {};\n__export(Wistia_exports, {\n  default: () => Wistia\n});\nmodule.exports = __toCommonJS(Wistia_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_utils = require(\"../utils\");\nvar import_patterns = require(\"../patterns\");\nconst SDK_URL = \"https://fast.wistia.com/assets/external/E-v1.js\";\nconst SDK_GLOBAL = \"Wistia\";\nconst PLAYER_ID_PREFIX = \"wistia-player-\";\nclass Wistia extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    __publicField(this, \"callPlayer\", import_utils.callPlayer);\n    __publicField(this, \"playerID\", this.props.config.playerId || `${PLAYER_ID_PREFIX}${(0, import_utils.randomString)()}`);\n    // Proxy methods to prevent listener leaks\n    __publicField(this, \"onPlay\", (...args) => this.props.onPlay(...args));\n    __publicField(this, \"onPause\", (...args) => this.props.onPause(...args));\n    __publicField(this, \"onSeek\", (...args) => this.props.onSeek(...args));\n    __publicField(this, \"onEnded\", (...args) => this.props.onEnded(...args));\n    __publicField(this, \"onPlaybackRateChange\", (...args) => this.props.onPlaybackRateChange(...args));\n    __publicField(this, \"mute\", () => {\n      this.callPlayer(\"mute\");\n    });\n    __publicField(this, \"unmute\", () => {\n      this.callPlayer(\"unmute\");\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n  }\n  load(url) {\n    const { playing, muted, controls, onReady, config, onError } = this.props;\n    (0, import_utils.getSDK)(SDK_URL, SDK_GLOBAL).then((Wistia2) => {\n      if (config.customControls) {\n        config.customControls.forEach((control) => Wistia2.defineControl(control));\n      }\n      window._wq = window._wq || [];\n      window._wq.push({\n        id: this.playerID,\n        options: {\n          autoPlay: playing,\n          silentAutoPlay: \"allow\",\n          muted,\n          controlsVisibleOnLoad: controls,\n          fullscreenButton: controls,\n          playbar: controls,\n          playbackRateControl: controls,\n          qualityControl: controls,\n          volumeControl: controls,\n          settingsControl: controls,\n          smallPlayButton: controls,\n          ...config.options\n        },\n        onReady: (player) => {\n          this.player = player;\n          this.unbind();\n          this.player.bind(\"play\", this.onPlay);\n          this.player.bind(\"pause\", this.onPause);\n          this.player.bind(\"seek\", this.onSeek);\n          this.player.bind(\"end\", this.onEnded);\n          this.player.bind(\"playbackratechange\", this.onPlaybackRateChange);\n          onReady();\n        }\n      });\n    }, onError);\n  }\n  unbind() {\n    this.player.unbind(\"play\", this.onPlay);\n    this.player.unbind(\"pause\", this.onPause);\n    this.player.unbind(\"seek\", this.onSeek);\n    this.player.unbind(\"end\", this.onEnded);\n    this.player.unbind(\"playbackratechange\", this.onPlaybackRateChange);\n  }\n  play() {\n    this.callPlayer(\"play\");\n  }\n  pause() {\n    this.callPlayer(\"pause\");\n  }\n  stop() {\n    this.unbind();\n    this.callPlayer(\"remove\");\n  }\n  seekTo(seconds, keepPlaying = true) {\n    this.callPlayer(\"time\", seconds);\n    if (!keepPlaying) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n    this.callPlayer(\"volume\", fraction);\n  }\n  setPlaybackRate(rate) {\n    this.callPlayer(\"playbackRate\", rate);\n  }\n  getDuration() {\n    return this.callPlayer(\"duration\");\n  }\n  getCurrentTime() {\n    return this.callPlayer(\"time\");\n  }\n  getSecondsLoaded() {\n    return null;\n  }\n  render() {\n    const { url } = this.props;\n    const videoID = url && url.match(import_patterns.MATCH_URL_WISTIA)[1];\n    const className = `wistia_embed wistia_async_${videoID}`;\n    const style = {\n      width: \"100%\",\n      height: \"100%\"\n    };\n    return /* @__PURE__ */ import_react.default.createElement(\"div\", { id: this.playerID, key: videoID, className, style });\n  }\n}\n__publicField(Wistia, \"displayName\", \"Wistia\");\n__publicField(Wistia, \"canPlay\", import_patterns.canPlay.wistia);\n__publicField(Wistia, \"loopOnEnded\", true);\n"], "mappings": ";;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,WAAW,OAAO;AACtB,QAAI,YAAY,OAAO;AACvB,QAAI,mBAAmB,OAAO;AAC9B,QAAI,oBAAoB,OAAO;AAC/B,QAAI,eAAe,OAAO;AAC1B,QAAI,eAAe,OAAO,UAAU;AACpC,QAAI,kBAAkB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,UAAU,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC1J,QAAI,WAAW,CAAC,QAAQ,QAAQ;AAC9B,eAAS,QAAQ;AACf,kBAAU,QAAQ,MAAM,EAAE,KAAK,IAAI,IAAI,GAAG,YAAY,KAAK,CAAC;AAAA,IAChE;AACA,QAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,UAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,iBAAS,OAAO,kBAAkB,IAAI;AACpC,cAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,sBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,MACvH;AACA,aAAO;AAAA,IACT;AACA,QAAI,UAAU,CAAC,KAAK,YAAY,YAAY,SAAS,OAAO,OAAO,SAAS,aAAa,GAAG,CAAC,IAAI,CAAC,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA,MAKnG,cAAc,CAAC,OAAO,CAAC,IAAI,aAAa,UAAU,QAAQ,WAAW,EAAE,OAAO,KAAK,YAAY,KAAK,CAAC,IAAI;AAAA,MACzG;AAAA,IACF;AACA,QAAI,eAAe,CAAC,QAAQ,YAAY,UAAU,CAAC,GAAG,cAAc,EAAE,OAAO,KAAK,CAAC,GAAG,GAAG;AACzF,QAAI,gBAAgB,CAAC,KAAK,KAAK,UAAU;AACvC,sBAAgB,KAAK,OAAO,QAAQ,WAAW,MAAM,KAAK,KAAK,KAAK;AACpE,aAAO;AAAA,IACT;AACA,QAAI,iBAAiB,CAAC;AACtB,aAAS,gBAAgB;AAAA,MACvB,SAAS,MAAM;AAAA,IACjB,CAAC;AACD,WAAO,UAAU,aAAa,cAAc;AAC5C,QAAI,eAAe,QAAQ,eAAgB;AAC3C,QAAI,eAAe;AACnB,QAAI,kBAAkB;AACtB,QAAM,UAAU;AAChB,QAAM,aAAa;AACnB,QAAM,mBAAmB;AACzB,QAAM,SAAN,cAAqB,aAAa,UAAU;AAAA,MAC1C,cAAc;AACZ,cAAM,GAAG,SAAS;AAClB,sBAAc,MAAM,cAAc,aAAa,UAAU;AACzD,sBAAc,MAAM,YAAY,KAAK,MAAM,OAAO,YAAY,GAAG,gBAAgB,IAAI,GAAG,aAAa,cAAc,CAAC,EAAE;AAEtH,sBAAc,MAAM,UAAU,IAAI,SAAS,KAAK,MAAM,OAAO,GAAG,IAAI,CAAC;AACrE,sBAAc,MAAM,WAAW,IAAI,SAAS,KAAK,MAAM,QAAQ,GAAG,IAAI,CAAC;AACvE,sBAAc,MAAM,UAAU,IAAI,SAAS,KAAK,MAAM,OAAO,GAAG,IAAI,CAAC;AACrE,sBAAc,MAAM,WAAW,IAAI,SAAS,KAAK,MAAM,QAAQ,GAAG,IAAI,CAAC;AACvE,sBAAc,MAAM,wBAAwB,IAAI,SAAS,KAAK,MAAM,qBAAqB,GAAG,IAAI,CAAC;AACjG,sBAAc,MAAM,QAAQ,MAAM;AAChC,eAAK,WAAW,MAAM;AAAA,QACxB,CAAC;AACD,sBAAc,MAAM,UAAU,MAAM;AAClC,eAAK,WAAW,QAAQ;AAAA,QAC1B,CAAC;AAAA,MACH;AAAA,MACA,oBAAoB;AAClB,aAAK,MAAM,WAAW,KAAK,MAAM,QAAQ,IAAI;AAAA,MAC/C;AAAA,MACA,KAAK,KAAK;AACR,cAAM,EAAE,SAAS,OAAO,UAAU,SAAS,QAAQ,QAAQ,IAAI,KAAK;AACpE,SAAC,GAAG,aAAa,QAAQ,SAAS,UAAU,EAAE,KAAK,CAAC,YAAY;AAC9D,cAAI,OAAO,gBAAgB;AACzB,mBAAO,eAAe,QAAQ,CAAC,YAAY,QAAQ,cAAc,OAAO,CAAC;AAAA,UAC3E;AACA,iBAAO,MAAM,OAAO,OAAO,CAAC;AAC5B,iBAAO,IAAI,KAAK;AAAA,YACd,IAAI,KAAK;AAAA,YACT,SAAS;AAAA,cACP,UAAU;AAAA,cACV,gBAAgB;AAAA,cAChB;AAAA,cACA,uBAAuB;AAAA,cACvB,kBAAkB;AAAA,cAClB,SAAS;AAAA,cACT,qBAAqB;AAAA,cACrB,gBAAgB;AAAA,cAChB,eAAe;AAAA,cACf,iBAAiB;AAAA,cACjB,iBAAiB;AAAA,cACjB,GAAG,OAAO;AAAA,YACZ;AAAA,YACA,SAAS,CAAC,WAAW;AACnB,mBAAK,SAAS;AACd,mBAAK,OAAO;AACZ,mBAAK,OAAO,KAAK,QAAQ,KAAK,MAAM;AACpC,mBAAK,OAAO,KAAK,SAAS,KAAK,OAAO;AACtC,mBAAK,OAAO,KAAK,QAAQ,KAAK,MAAM;AACpC,mBAAK,OAAO,KAAK,OAAO,KAAK,OAAO;AACpC,mBAAK,OAAO,KAAK,sBAAsB,KAAK,oBAAoB;AAChE,sBAAQ;AAAA,YACV;AAAA,UACF,CAAC;AAAA,QACH,GAAG,OAAO;AAAA,MACZ;AAAA,MACA,SAAS;AACP,aAAK,OAAO,OAAO,QAAQ,KAAK,MAAM;AACtC,aAAK,OAAO,OAAO,SAAS,KAAK,OAAO;AACxC,aAAK,OAAO,OAAO,QAAQ,KAAK,MAAM;AACtC,aAAK,OAAO,OAAO,OAAO,KAAK,OAAO;AACtC,aAAK,OAAO,OAAO,sBAAsB,KAAK,oBAAoB;AAAA,MACpE;AAAA,MACA,OAAO;AACL,aAAK,WAAW,MAAM;AAAA,MACxB;AAAA,MACA,QAAQ;AACN,aAAK,WAAW,OAAO;AAAA,MACzB;AAAA,MACA,OAAO;AACL,aAAK,OAAO;AACZ,aAAK,WAAW,QAAQ;AAAA,MAC1B;AAAA,MACA,OAAO,SAAS,cAAc,MAAM;AAClC,aAAK,WAAW,QAAQ,OAAO;AAC/B,YAAI,CAAC,aAAa;AAChB,eAAK,MAAM;AAAA,QACb;AAAA,MACF;AAAA,MACA,UAAU,UAAU;AAClB,aAAK,WAAW,UAAU,QAAQ;AAAA,MACpC;AAAA,MACA,gBAAgB,MAAM;AACpB,aAAK,WAAW,gBAAgB,IAAI;AAAA,MACtC;AAAA,MACA,cAAc;AACZ,eAAO,KAAK,WAAW,UAAU;AAAA,MACnC;AAAA,MACA,iBAAiB;AACf,eAAO,KAAK,WAAW,MAAM;AAAA,MAC/B;AAAA,MACA,mBAAmB;AACjB,eAAO;AAAA,MACT;AAAA,MACA,SAAS;AACP,cAAM,EAAE,IAAI,IAAI,KAAK;AACrB,cAAM,UAAU,OAAO,IAAI,MAAM,gBAAgB,gBAAgB,EAAE,CAAC;AACpE,cAAM,YAAY,6BAA6B,OAAO;AACtD,cAAM,QAAQ;AAAA,UACZ,OAAO;AAAA,UACP,QAAQ;AAAA,QACV;AACA,eAAuB,aAAa,QAAQ,cAAc,OAAO,EAAE,IAAI,KAAK,UAAU,KAAK,SAAS,WAAW,MAAM,CAAC;AAAA,MACxH;AAAA,IACF;AACA,kBAAc,QAAQ,eAAe,QAAQ;AAC7C,kBAAc,QAAQ,WAAW,gBAAgB,QAAQ,MAAM;AAC/D,kBAAc,QAAQ,eAAe,IAAI;AAAA;AAAA;", "names": []}