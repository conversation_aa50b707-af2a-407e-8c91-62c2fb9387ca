import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  Box,
  TextField,
  Autocomplete,
  Chip,
  Paper,
  Typography,
  Grid,
  Slider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Button,
  Collapse,
  IconButton,
  Divider,
  Rating
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  Clear as ClearIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon
} from '@mui/icons-material';
import { useDebounce } from '../../hooks/usePerformance';
import { enhancedAPI } from '../../services/api';

const AdvancedSearch = ({ 
  onSearch, 
  onFilterChange, 
  initialFilters = {},
  searchPlaceholder = "Search courses...",
  showAdvancedFilters = true 
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    category: '',
    level: '',
    priceRange: [0, 200],
    rating: 0,
    duration: '',
    language: '',
    instructor: '',
    tags: [],
    isFree: false,
    hasVideo: false,
    hasCertificate: false,
    ...initialFilters
  });
  const [showFilters, setShowFilters] = useState(false);
  const [suggestions, setSuggestions] = useState([]);
  const [recentSearches, setRecentSearches] = useState([]);

  // Debounce search term
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  // Load recent searches from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('recentSearches');
    if (saved) {
      setRecentSearches(JSON.parse(saved));
    }
  }, []);

  // Save recent searches
  const saveRecentSearch = useCallback((term) => {
    if (!term.trim()) return;
    
    const updated = [term, ...recentSearches.filter(s => s !== term)].slice(0, 5);
    setRecentSearches(updated);
    localStorage.setItem('recentSearches', JSON.stringify(updated));
  }, [recentSearches]);

  // Handle search
  useEffect(() => {
    if (debouncedSearchTerm !== undefined) {
      onSearch?.(debouncedSearchTerm, filters);
      if (debouncedSearchTerm) {
        saveRecentSearch(debouncedSearchTerm);
      }
    }
  }, [debouncedSearchTerm, filters, onSearch, saveRecentSearch]);

  // Handle filter changes
  useEffect(() => {
    onFilterChange?.(filters);
  }, [filters, onFilterChange]);

  // Get search suggestions
  const getSuggestions = useCallback(async (term) => {
    if (!term || term.length < 2) {
      setSuggestions([]);
      return;
    }

    try {
      // In a real app, this would call an API endpoint
      const mockSuggestions = [
        'React Development',
        'JavaScript Fundamentals',
        'Web Development',
        'UI/UX Design',
        'Node.js Backend',
        'Python Programming',
        'Data Science',
        'Machine Learning'
      ].filter(s => s.toLowerCase().includes(term.toLowerCase()));

      setSuggestions(mockSuggestions);
    } catch (error) {
      console.error('Error getting suggestions:', error);
    }
  }, []);

  // Debounced suggestions
  useEffect(() => {
    getSuggestions(searchTerm);
  }, [searchTerm, getSuggestions]);

  const handleFilterChange = useCallback((key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  }, []);

  const handleTagAdd = useCallback((tag) => {
    if (tag && !filters.tags.includes(tag)) {
      handleFilterChange('tags', [...filters.tags, tag]);
    }
  }, [filters.tags, handleFilterChange]);

  const handleTagRemove = useCallback((tagToRemove) => {
    handleFilterChange('tags', filters.tags.filter(tag => tag !== tagToRemove));
  }, [filters.tags, handleFilterChange]);

  const clearAllFilters = useCallback(() => {
    setFilters({
      category: '',
      level: '',
      priceRange: [0, 200],
      rating: 0,
      duration: '',
      language: '',
      instructor: '',
      tags: [],
      isFree: false,
      hasVideo: false,
      hasCertificate: false
    });
    setSearchTerm('');
  }, []);

  const activeFiltersCount = useMemo(() => {
    return Object.entries(filters).reduce((count, [key, value]) => {
      if (key === 'priceRange') {
        return count + (value[0] > 0 || value[1] < 200 ? 1 : 0);
      }
      if (Array.isArray(value)) {
        return count + (value.length > 0 ? 1 : 0);
      }
      if (typeof value === 'boolean') {
        return count + (value ? 1 : 0);
      }
      return count + (value ? 1 : 0);
    }, 0);
  }, [filters]);

  const categories = [
    'Programming', 'Design', 'Business', 'Marketing', 
    'Photography', 'Music', 'Health & Fitness', 'Language'
  ];

  const levels = ['Beginner', 'Intermediate', 'Advanced'];
  const languages = ['English', 'Spanish', 'French', 'German', 'Chinese'];
  const durations = ['0-2 hours', '2-6 hours', '6-17 hours', '17+ hours'];

  return (
    <Paper sx={{ p: 3, mb: 3 }}>
      {/* Main Search Bar */}
      <Box sx={{ mb: 2 }}>
        <Autocomplete
          freeSolo
          options={[...suggestions, ...recentSearches]}
          value={searchTerm}
          onInputChange={(event, newValue) => {
            setSearchTerm(newValue || '');
          }}
          renderInput={(params) => (
            <TextField
              {...params}
              fullWidth
              placeholder={searchPlaceholder}
              InputProps={{
                ...params.InputProps,
                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
                endAdornment: (
                  <Box display="flex" alignItems="center">
                    {searchTerm && (
                      <IconButton
                        size="small"
                        onClick={() => setSearchTerm('')}
                      >
                        <ClearIcon />
                      </IconButton>
                    )}
                    {showAdvancedFilters && (
                      <IconButton
                        onClick={() => setShowFilters(!showFilters)}
                        color={activeFiltersCount > 0 ? 'primary' : 'default'}
                      >
                        <FilterIcon />
                        {activeFiltersCount > 0 && (
                          <Chip
                            label={activeFiltersCount}
                            size="small"
                            color="primary"
                            sx={{ ml: 0.5, height: 16, fontSize: '0.7rem' }}
                          />
                        )}
                      </IconButton>
                    )}
                  </Box>
                )
              }}
            />
          )}
          renderOption={(props, option) => (
            <Box component="li" {...props}>
              <Box>
                <Typography variant="body2">{option}</Typography>
                {recentSearches.includes(option) && (
                  <Typography variant="caption" color="text.secondary">
                    Recent search
                  </Typography>
                )}
              </Box>
            </Box>
          )}
        />
      </Box>

      {/* Advanced Filters */}
      {showAdvancedFilters && (
        <Collapse in={showFilters}>
          <Box sx={{ pt: 2 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" sx={{ mb: 2 }}>
              <Typography variant="h6">
                Filters
                {activeFiltersCount > 0 && (
                  <Chip
                    label={`${activeFiltersCount} active`}
                    size="small"
                    color="primary"
                    sx={{ ml: 1 }}
                  />
                )}
              </Typography>
              <Button
                size="small"
                onClick={clearAllFilters}
                disabled={activeFiltersCount === 0}
              >
                Clear All
              </Button>
            </Box>

            <Grid container spacing={3}>
              {/* Category */}
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Category</InputLabel>
                  <Select
                    value={filters.category}
                    onChange={(e) => handleFilterChange('category', e.target.value)}
                    label="Category"
                  >
                    <MenuItem value="">All Categories</MenuItem>
                    {categories.map((category) => (
                      <MenuItem key={category} value={category}>
                        {category}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              {/* Level */}
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Level</InputLabel>
                  <Select
                    value={filters.level}
                    onChange={(e) => handleFilterChange('level', e.target.value)}
                    label="Level"
                  >
                    <MenuItem value="">All Levels</MenuItem>
                    {levels.map((level) => (
                      <MenuItem key={level} value={level}>
                        {level}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              {/* Language */}
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Language</InputLabel>
                  <Select
                    value={filters.language}
                    onChange={(e) => handleFilterChange('language', e.target.value)}
                    label="Language"
                  >
                    <MenuItem value="">All Languages</MenuItem>
                    {languages.map((language) => (
                      <MenuItem key={language} value={language}>
                        {language}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              {/* Duration */}
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Duration</InputLabel>
                  <Select
                    value={filters.duration}
                    onChange={(e) => handleFilterChange('duration', e.target.value)}
                    label="Duration"
                  >
                    <MenuItem value="">Any Duration</MenuItem>
                    {durations.map((duration) => (
                      <MenuItem key={duration} value={duration}>
                        {duration}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              {/* Price Range */}
              <Grid item xs={12} sm={6}>
                <Typography gutterBottom>
                  Price Range: ${filters.priceRange[0]} - ${filters.priceRange[1]}
                </Typography>
                <Slider
                  value={filters.priceRange}
                  onChange={(e, newValue) => handleFilterChange('priceRange', newValue)}
                  valueLabelDisplay="auto"
                  min={0}
                  max={200}
                  step={10}
                />
              </Grid>

              {/* Rating */}
              <Grid item xs={12} sm={6}>
                <Typography gutterBottom>Minimum Rating</Typography>
                <Rating
                  value={filters.rating}
                  onChange={(e, newValue) => handleFilterChange('rating', newValue || 0)}
                />
              </Grid>

              {/* Boolean Filters */}
              <Grid item xs={12}>
                <Box display="flex" flexWrap="wrap" gap={2}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={filters.isFree}
                        onChange={(e) => handleFilterChange('isFree', e.target.checked)}
                      />
                    }
                    label="Free Courses Only"
                  />
                  <FormControlLabel
                    control={
                      <Switch
                        checked={filters.hasVideo}
                        onChange={(e) => handleFilterChange('hasVideo', e.target.checked)}
                      />
                    }
                    label="Has Video Content"
                  />
                  <FormControlLabel
                    control={
                      <Switch
                        checked={filters.hasCertificate}
                        onChange={(e) => handleFilterChange('hasCertificate', e.target.checked)}
                      />
                    }
                    label="Offers Certificate"
                  />
                </Box>
              </Grid>

              {/* Tags */}
              <Grid item xs={12}>
                <Typography gutterBottom>Tags</Typography>
                <Box display="flex" flexWrap="wrap" gap={1} sx={{ mb: 1 }}>
                  {filters.tags.map((tag) => (
                    <Chip
                      key={tag}
                      label={tag}
                      onDelete={() => handleTagRemove(tag)}
                      color="primary"
                      variant="outlined"
                    />
                  ))}
                </Box>
                <TextField
                  size="small"
                  placeholder="Add tag and press Enter"
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      handleTagAdd(e.target.value);
                      e.target.value = '';
                    }
                  }}
                />
              </Grid>
            </Grid>
          </Box>
        </Collapse>
      )}
    </Paper>
  );
};

export default AdvancedSearch;
