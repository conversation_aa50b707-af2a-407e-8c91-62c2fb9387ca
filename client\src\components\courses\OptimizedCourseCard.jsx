import React, { memo, useState, useCallback } from 'react';
import {
  Card,
  CardContent,
  CardMedia,
  Typography,
  Box,
  Chip,
  Rating,
  Button,
  Skeleton,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Favorite as FavoriteIcon,
  FavoriteBorder as FavoriteBorderIcon,
  Share as ShareIcon,
  PlayArrow as PlayIcon
} from '@mui/icons-material';
import { Link } from 'react-router-dom';
import { useLazyImage, useIntersectionObserver } from '../../hooks/usePerformance';

const OptimizedCourseCard = memo(({ 
  course, 
  onFavorite, 
  onShare, 
  isFavorited = false,
  showPreview = false 
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [ref, isVisible] = useIntersectionObserver({
    threshold: 0.1,
    rootMargin: '50px'
  });

  // Lazy load image only when card is visible
  const { imageSrc, isLoaded, isError } = useLazyImage(
    isVisible ? course.thumbnail : '',
    'https://via.placeholder.com/300x200?text=Loading...'
  );

  const handleFavorite = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    onFavorite?.(course.id);
  }, [course.id, onFavorite]);

  const handleShare = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    onShare?.(course);
  }, [course, onShare]);

  const handleMouseEnter = useCallback(() => {
    setIsHovered(true);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setIsHovered(false);
  }, []);

  // Memoized price formatting
  const formattedPrice = React.useMemo(() => {
    return course.price === 0 ? 'Free' : `$${course.price}`;
  }, [course.price]);

  // Memoized rating display
  const ratingDisplay = React.useMemo(() => {
    return (
      <Box display="flex" alignItems="center" sx={{ mb: 1 }}>
        <Rating 
          value={course.rating} 
          precision={0.1} 
          size="small" 
          readOnly 
        />
        <Typography variant="body2" sx={{ ml: 1 }}>
          {course.rating} ({course.students} students)
        </Typography>
      </Box>
    );
  }, [course.rating, course.students]);

  return (
    <Card
      ref={ref}
      component={Link}
      to={`/courses/${course.id}`}
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        textDecoration: 'none',
        color: 'inherit',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        transform: isHovered ? 'translateY(-8px)' : 'translateY(0)',
        boxShadow: isHovered ? 4 : 1,
        '&:hover': {
          textDecoration: 'none',
          color: 'inherit'
        },
        position: 'relative',
        overflow: 'hidden'
      }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Image Container */}
      <Box sx={{ position: 'relative', overflow: 'hidden' }}>
        {!isVisible || !isLoaded ? (
          <Skeleton 
            variant="rectangular" 
            height={200} 
            animation="wave"
          />
        ) : (
          <>
            <CardMedia
              component="img"
              height="200"
              image={isError ? 'https://via.placeholder.com/300x200?text=Image+Error' : imageSrc}
              alt={course.title}
              sx={{
                transition: 'transform 0.3s ease',
                transform: isHovered ? 'scale(1.05)' : 'scale(1)'
              }}
            />
            
            {/* Overlay with actions */}
            <Box
              sx={{
                position: 'absolute',
                top: 8,
                right: 8,
                display: 'flex',
                gap: 1,
                opacity: isHovered ? 1 : 0,
                transition: 'opacity 0.3s ease'
              }}
            >
              <Tooltip title={isFavorited ? 'Remove from favorites' : 'Add to favorites'}>
                <IconButton
                  size="small"
                  onClick={handleFavorite}
                  sx={{
                    bgcolor: 'rgba(255, 255, 255, 0.9)',
                    '&:hover': { bgcolor: 'rgba(255, 255, 255, 1)' }
                  }}
                >
                  {isFavorited ? (
                    <FavoriteIcon color="error" />
                  ) : (
                    <FavoriteBorderIcon />
                  )}
                </IconButton>
              </Tooltip>
              
              <Tooltip title="Share course">
                <IconButton
                  size="small"
                  onClick={handleShare}
                  sx={{
                    bgcolor: 'rgba(255, 255, 255, 0.9)',
                    '&:hover': { bgcolor: 'rgba(255, 255, 255, 1)' }
                  }}
                >
                  <ShareIcon />
                </IconButton>
              </Tooltip>
            </Box>

            {/* Preview button */}
            {showPreview && (
              <Box
                sx={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  opacity: isHovered ? 1 : 0,
                  transition: 'opacity 0.3s ease'
                }}
              >
                <IconButton
                  size="large"
                  sx={{
                    bgcolor: 'rgba(0, 0, 0, 0.7)',
                    color: 'white',
                    '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.8)' }
                  }}
                >
                  <PlayIcon sx={{ fontSize: 40 }} />
                </IconButton>
              </Box>
            )}

            {/* Category and Level chips */}
            <Box
              sx={{
                position: 'absolute',
                bottom: 8,
                left: 8,
                display: 'flex',
                gap: 1
              }}
            >
              <Chip
                label={course.category}
                size="small"
                color="primary"
                sx={{ bgcolor: 'rgba(25, 118, 210, 0.9)' }}
              />
              <Chip
                label={course.level}
                size="small"
                variant="outlined"
                sx={{ 
                  bgcolor: 'rgba(255, 255, 255, 0.9)',
                  borderColor: 'rgba(255, 255, 255, 0.9)'
                }}
              />
            </Box>
          </>
        )}
      </Box>

      {/* Content */}
      <CardContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
        <Typography 
          variant="h6" 
          component="h3" 
          gutterBottom
          sx={{
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical',
            lineHeight: 1.2,
            height: '2.4em'
          }}
        >
          {course.title}
        </Typography>
        
        <Typography 
          variant="body2" 
          color="text.secondary" 
          sx={{ 
            mb: 2, 
            flexGrow: 1,
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical'
          }}
        >
          {course.description}
        </Typography>
        
        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
          by {course.instructor}
        </Typography>
        
        {ratingDisplay}
        
        <Box display="flex" justifyContent="space-between" alignItems="center" sx={{ mt: 'auto' }}>
          <Typography 
            variant="h6" 
            color="primary" 
            fontWeight="bold"
            sx={{
              fontSize: '1.25rem'
            }}
          >
            {formattedPrice}
          </Typography>
          
          <Button
            variant="contained"
            size="small"
            sx={{
              opacity: isHovered ? 1 : 0.8,
              transition: 'opacity 0.3s ease'
            }}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              // Handle quick enroll
            }}
          >
            {course.price === 0 ? 'Enroll Free' : 'View Course'}
          </Button>
        </Box>
      </CardContent>

      {/* Loading overlay */}
      {!isVisible && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            bgcolor: 'rgba(255, 255, 255, 0.8)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          <Skeleton variant="rectangular" width="100%" height="100%" />
        </Box>
      )}
    </Card>
  );
});

OptimizedCourseCard.displayName = 'OptimizedCourseCard';

export default OptimizedCourseCard;
