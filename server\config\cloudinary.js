const cloudinary = require('cloudinary').v2;

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

// Upload video to Cloudinary
const uploadVideo = async (filePath, options = {}) => {
  try {
    const result = await cloudinary.uploader.upload(filePath, {
      resource_type: 'video',
      folder: 'lms/videos',
      ...options
    });
    return result;
  } catch (error) {
    console.error('Video upload error:', error);
    throw error;
  }
};

// Upload image to Cloudinary
const uploadImage = async (filePath, options = {}) => {
  try {
    const result = await cloudinary.uploader.upload(filePath, {
      resource_type: 'image',
      folder: 'lms/images',
      ...options
    });
    return result;
  } catch (error) {
    console.error('Image upload error:', error);
    throw error;
  }
};

// Delete file from Cloudinary
const deleteFile = async (publicId, resourceType = 'image') => {
  try {
    const result = await cloudinary.uploader.destroy(publicId, {
      resource_type: resourceType
    });
    return result;
  } catch (error) {
    console.error('File deletion error:', error);
    throw error;
  }
};

module.exports = {
  cloudinary,
  uploadVideo,
  uploadImage,
  deleteFile
};
