{"version": 3, "sources": ["../../react-player/lib/players/Mux.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar Mux_exports = {};\n__export(Mux_exports, {\n  default: () => Mux\n});\nmodule.exports = __toCommonJS(Mux_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_patterns = require(\"../patterns\");\nconst SDK_URL = \"https://cdn.jsdelivr.net/npm/@mux/mux-player@VERSION/dist/mux-player.mjs\";\nclass Mux extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    // Proxy methods to prevent listener leaks\n    __publicField(this, \"onReady\", (...args) => this.props.onReady(...args));\n    __publicField(this, \"onPlay\", (...args) => this.props.onPlay(...args));\n    __publicField(this, \"onBuffer\", (...args) => this.props.onBuffer(...args));\n    __publicField(this, \"onBufferEnd\", (...args) => this.props.onBufferEnd(...args));\n    __publicField(this, \"onPause\", (...args) => this.props.onPause(...args));\n    __publicField(this, \"onEnded\", (...args) => this.props.onEnded(...args));\n    __publicField(this, \"onError\", (...args) => this.props.onError(...args));\n    __publicField(this, \"onPlayBackRateChange\", (event) => this.props.onPlaybackRateChange(event.target.playbackRate));\n    __publicField(this, \"onEnablePIP\", (...args) => this.props.onEnablePIP(...args));\n    __publicField(this, \"onSeek\", (e) => {\n      this.props.onSeek(e.target.currentTime);\n    });\n    __publicField(this, \"onDurationChange\", () => {\n      const duration = this.getDuration();\n      this.props.onDuration(duration);\n    });\n    __publicField(this, \"mute\", () => {\n      this.player.muted = true;\n    });\n    __publicField(this, \"unmute\", () => {\n      this.player.muted = false;\n    });\n    __publicField(this, \"ref\", (player) => {\n      this.player = player;\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n    this.addListeners(this.player);\n    const playbackId = this.getPlaybackId(this.props.url);\n    if (playbackId) {\n      this.player.playbackId = playbackId;\n    }\n  }\n  componentWillUnmount() {\n    this.player.playbackId = null;\n    this.removeListeners(this.player);\n  }\n  addListeners(player) {\n    const { playsinline } = this.props;\n    player.addEventListener(\"play\", this.onPlay);\n    player.addEventListener(\"waiting\", this.onBuffer);\n    player.addEventListener(\"playing\", this.onBufferEnd);\n    player.addEventListener(\"pause\", this.onPause);\n    player.addEventListener(\"seeked\", this.onSeek);\n    player.addEventListener(\"ended\", this.onEnded);\n    player.addEventListener(\"error\", this.onError);\n    player.addEventListener(\"ratechange\", this.onPlayBackRateChange);\n    player.addEventListener(\"enterpictureinpicture\", this.onEnablePIP);\n    player.addEventListener(\"leavepictureinpicture\", this.onDisablePIP);\n    player.addEventListener(\"webkitpresentationmodechanged\", this.onPresentationModeChange);\n    player.addEventListener(\"canplay\", this.onReady);\n    if (playsinline) {\n      player.setAttribute(\"playsinline\", \"\");\n    }\n  }\n  removeListeners(player) {\n    player.removeEventListener(\"canplay\", this.onReady);\n    player.removeEventListener(\"play\", this.onPlay);\n    player.removeEventListener(\"waiting\", this.onBuffer);\n    player.removeEventListener(\"playing\", this.onBufferEnd);\n    player.removeEventListener(\"pause\", this.onPause);\n    player.removeEventListener(\"seeked\", this.onSeek);\n    player.removeEventListener(\"ended\", this.onEnded);\n    player.removeEventListener(\"error\", this.onError);\n    player.removeEventListener(\"ratechange\", this.onPlayBackRateChange);\n    player.removeEventListener(\"enterpictureinpicture\", this.onEnablePIP);\n    player.removeEventListener(\"leavepictureinpicture\", this.onDisablePIP);\n    player.removeEventListener(\"canplay\", this.onReady);\n  }\n  async load(url) {\n    var _a;\n    const { onError, config } = this.props;\n    if (!((_a = globalThis.customElements) == null ? void 0 : _a.get(\"mux-player\"))) {\n      try {\n        const sdkUrl = SDK_URL.replace(\"VERSION\", config.version);\n        await import(\n          /* webpackIgnore: true */\n          `${sdkUrl}`\n        );\n        this.props.onLoaded();\n      } catch (error) {\n        onError(error);\n      }\n    }\n    const [, id] = url.match(import_patterns.MATCH_URL_MUX);\n    this.player.playbackId = id;\n  }\n  play() {\n    const promise = this.player.play();\n    if (promise) {\n      promise.catch(this.props.onError);\n    }\n  }\n  pause() {\n    this.player.pause();\n  }\n  stop() {\n    this.player.playbackId = null;\n  }\n  seekTo(seconds, keepPlaying = true) {\n    this.player.currentTime = seconds;\n    if (!keepPlaying) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n    this.player.volume = fraction;\n  }\n  enablePIP() {\n    if (this.player.requestPictureInPicture && document.pictureInPictureElement !== this.player) {\n      this.player.requestPictureInPicture();\n    }\n  }\n  disablePIP() {\n    if (document.exitPictureInPicture && document.pictureInPictureElement === this.player) {\n      document.exitPictureInPicture();\n    }\n  }\n  setPlaybackRate(rate) {\n    try {\n      this.player.playbackRate = rate;\n    } catch (error) {\n      this.props.onError(error);\n    }\n  }\n  getDuration() {\n    if (!this.player)\n      return null;\n    const { duration, seekable } = this.player;\n    if (duration === Infinity && seekable.length > 0) {\n      return seekable.end(seekable.length - 1);\n    }\n    return duration;\n  }\n  getCurrentTime() {\n    if (!this.player)\n      return null;\n    return this.player.currentTime;\n  }\n  getSecondsLoaded() {\n    if (!this.player)\n      return null;\n    const { buffered } = this.player;\n    if (buffered.length === 0) {\n      return 0;\n    }\n    const end = buffered.end(buffered.length - 1);\n    const duration = this.getDuration();\n    if (end > duration) {\n      return duration;\n    }\n    return end;\n  }\n  getPlaybackId(url) {\n    const [, id] = url.match(import_patterns.MATCH_URL_MUX);\n    return id;\n  }\n  render() {\n    const { url, playing, loop, controls, muted, config, width, height } = this.props;\n    const style = {\n      width: width === \"auto\" ? width : \"100%\",\n      height: height === \"auto\" ? height : \"100%\"\n    };\n    if (controls === false) {\n      style[\"--controls\"] = \"none\";\n    }\n    return /* @__PURE__ */ import_react.default.createElement(\n      \"mux-player\",\n      {\n        ref: this.ref,\n        \"playback-id\": this.getPlaybackId(url),\n        style,\n        preload: \"auto\",\n        autoPlay: playing || void 0,\n        muted: muted ? \"\" : void 0,\n        loop: loop ? \"\" : void 0,\n        ...config.attributes\n      }\n    );\n  }\n}\n__publicField(Mux, \"displayName\", \"Mux\");\n__publicField(Mux, \"canPlay\", import_patterns.canPlay.mux);\n"], "mappings": ";;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,WAAW,OAAO;AACtB,QAAI,YAAY,OAAO;AACvB,QAAI,mBAAmB,OAAO;AAC9B,QAAI,oBAAoB,OAAO;AAC/B,QAAI,eAAe,OAAO;AAC1B,QAAI,eAAe,OAAO,UAAU;AACpC,QAAI,kBAAkB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,UAAU,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC1J,QAAI,WAAW,CAAC,QAAQ,QAAQ;AAC9B,eAAS,QAAQ;AACf,kBAAU,QAAQ,MAAM,EAAE,KAAK,IAAI,IAAI,GAAG,YAAY,KAAK,CAAC;AAAA,IAChE;AACA,QAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,UAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,iBAAS,OAAO,kBAAkB,IAAI;AACpC,cAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,sBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,MACvH;AACA,aAAO;AAAA,IACT;AACA,QAAI,UAAU,CAAC,KAAK,YAAY,YAAY,SAAS,OAAO,OAAO,SAAS,aAAa,GAAG,CAAC,IAAI,CAAC,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA,MAKnG,cAAc,CAAC,OAAO,CAAC,IAAI,aAAa,UAAU,QAAQ,WAAW,EAAE,OAAO,KAAK,YAAY,KAAK,CAAC,IAAI;AAAA,MACzG;AAAA,IACF;AACA,QAAI,eAAe,CAAC,QAAQ,YAAY,UAAU,CAAC,GAAG,cAAc,EAAE,OAAO,KAAK,CAAC,GAAG,GAAG;AACzF,QAAI,gBAAgB,CAAC,KAAK,KAAK,UAAU;AACvC,sBAAgB,KAAK,OAAO,QAAQ,WAAW,MAAM,KAAK,KAAK,KAAK;AACpE,aAAO;AAAA,IACT;AACA,QAAI,cAAc,CAAC;AACnB,aAAS,aAAa;AAAA,MACpB,SAAS,MAAM;AAAA,IACjB,CAAC;AACD,WAAO,UAAU,aAAa,WAAW;AACzC,QAAI,eAAe,QAAQ,eAAgB;AAC3C,QAAI,kBAAkB;AACtB,QAAM,UAAU;AAChB,QAAM,MAAN,cAAkB,aAAa,UAAU;AAAA,MACvC,cAAc;AACZ,cAAM,GAAG,SAAS;AAElB,sBAAc,MAAM,WAAW,IAAI,SAAS,KAAK,MAAM,QAAQ,GAAG,IAAI,CAAC;AACvE,sBAAc,MAAM,UAAU,IAAI,SAAS,KAAK,MAAM,OAAO,GAAG,IAAI,CAAC;AACrE,sBAAc,MAAM,YAAY,IAAI,SAAS,KAAK,MAAM,SAAS,GAAG,IAAI,CAAC;AACzE,sBAAc,MAAM,eAAe,IAAI,SAAS,KAAK,MAAM,YAAY,GAAG,IAAI,CAAC;AAC/E,sBAAc,MAAM,WAAW,IAAI,SAAS,KAAK,MAAM,QAAQ,GAAG,IAAI,CAAC;AACvE,sBAAc,MAAM,WAAW,IAAI,SAAS,KAAK,MAAM,QAAQ,GAAG,IAAI,CAAC;AACvE,sBAAc,MAAM,WAAW,IAAI,SAAS,KAAK,MAAM,QAAQ,GAAG,IAAI,CAAC;AACvE,sBAAc,MAAM,wBAAwB,CAAC,UAAU,KAAK,MAAM,qBAAqB,MAAM,OAAO,YAAY,CAAC;AACjH,sBAAc,MAAM,eAAe,IAAI,SAAS,KAAK,MAAM,YAAY,GAAG,IAAI,CAAC;AAC/E,sBAAc,MAAM,UAAU,CAAC,MAAM;AACnC,eAAK,MAAM,OAAO,EAAE,OAAO,WAAW;AAAA,QACxC,CAAC;AACD,sBAAc,MAAM,oBAAoB,MAAM;AAC5C,gBAAM,WAAW,KAAK,YAAY;AAClC,eAAK,MAAM,WAAW,QAAQ;AAAA,QAChC,CAAC;AACD,sBAAc,MAAM,QAAQ,MAAM;AAChC,eAAK,OAAO,QAAQ;AAAA,QACtB,CAAC;AACD,sBAAc,MAAM,UAAU,MAAM;AAClC,eAAK,OAAO,QAAQ;AAAA,QACtB,CAAC;AACD,sBAAc,MAAM,OAAO,CAAC,WAAW;AACrC,eAAK,SAAS;AAAA,QAChB,CAAC;AAAA,MACH;AAAA,MACA,oBAAoB;AAClB,aAAK,MAAM,WAAW,KAAK,MAAM,QAAQ,IAAI;AAC7C,aAAK,aAAa,KAAK,MAAM;AAC7B,cAAM,aAAa,KAAK,cAAc,KAAK,MAAM,GAAG;AACpD,YAAI,YAAY;AACd,eAAK,OAAO,aAAa;AAAA,QAC3B;AAAA,MACF;AAAA,MACA,uBAAuB;AACrB,aAAK,OAAO,aAAa;AACzB,aAAK,gBAAgB,KAAK,MAAM;AAAA,MAClC;AAAA,MACA,aAAa,QAAQ;AACnB,cAAM,EAAE,YAAY,IAAI,KAAK;AAC7B,eAAO,iBAAiB,QAAQ,KAAK,MAAM;AAC3C,eAAO,iBAAiB,WAAW,KAAK,QAAQ;AAChD,eAAO,iBAAiB,WAAW,KAAK,WAAW;AACnD,eAAO,iBAAiB,SAAS,KAAK,OAAO;AAC7C,eAAO,iBAAiB,UAAU,KAAK,MAAM;AAC7C,eAAO,iBAAiB,SAAS,KAAK,OAAO;AAC7C,eAAO,iBAAiB,SAAS,KAAK,OAAO;AAC7C,eAAO,iBAAiB,cAAc,KAAK,oBAAoB;AAC/D,eAAO,iBAAiB,yBAAyB,KAAK,WAAW;AACjE,eAAO,iBAAiB,yBAAyB,KAAK,YAAY;AAClE,eAAO,iBAAiB,iCAAiC,KAAK,wBAAwB;AACtF,eAAO,iBAAiB,WAAW,KAAK,OAAO;AAC/C,YAAI,aAAa;AACf,iBAAO,aAAa,eAAe,EAAE;AAAA,QACvC;AAAA,MACF;AAAA,MACA,gBAAgB,QAAQ;AACtB,eAAO,oBAAoB,WAAW,KAAK,OAAO;AAClD,eAAO,oBAAoB,QAAQ,KAAK,MAAM;AAC9C,eAAO,oBAAoB,WAAW,KAAK,QAAQ;AACnD,eAAO,oBAAoB,WAAW,KAAK,WAAW;AACtD,eAAO,oBAAoB,SAAS,KAAK,OAAO;AAChD,eAAO,oBAAoB,UAAU,KAAK,MAAM;AAChD,eAAO,oBAAoB,SAAS,KAAK,OAAO;AAChD,eAAO,oBAAoB,SAAS,KAAK,OAAO;AAChD,eAAO,oBAAoB,cAAc,KAAK,oBAAoB;AAClE,eAAO,oBAAoB,yBAAyB,KAAK,WAAW;AACpE,eAAO,oBAAoB,yBAAyB,KAAK,YAAY;AACrE,eAAO,oBAAoB,WAAW,KAAK,OAAO;AAAA,MACpD;AAAA,MACA,MAAM,KAAK,KAAK;AACd,YAAI;AACJ,cAAM,EAAE,SAAS,OAAO,IAAI,KAAK;AACjC,YAAI,GAAG,KAAK,WAAW,mBAAmB,OAAO,SAAS,GAAG,IAAI,YAAY,IAAI;AAC/E,cAAI;AACF,kBAAM,SAAS,QAAQ,QAAQ,WAAW,OAAO,OAAO;AACxD,kBAAM;AAAA;AAAA,cAEJ,GAAG,MAAM;AAAA;AAEX,iBAAK,MAAM,SAAS;AAAA,UACtB,SAAS,OAAO;AACd,oBAAQ,KAAK;AAAA,UACf;AAAA,QACF;AACA,cAAM,CAAC,EAAE,EAAE,IAAI,IAAI,MAAM,gBAAgB,aAAa;AACtD,aAAK,OAAO,aAAa;AAAA,MAC3B;AAAA,MACA,OAAO;AACL,cAAM,UAAU,KAAK,OAAO,KAAK;AACjC,YAAI,SAAS;AACX,kBAAQ,MAAM,KAAK,MAAM,OAAO;AAAA,QAClC;AAAA,MACF;AAAA,MACA,QAAQ;AACN,aAAK,OAAO,MAAM;AAAA,MACpB;AAAA,MACA,OAAO;AACL,aAAK,OAAO,aAAa;AAAA,MAC3B;AAAA,MACA,OAAO,SAAS,cAAc,MAAM;AAClC,aAAK,OAAO,cAAc;AAC1B,YAAI,CAAC,aAAa;AAChB,eAAK,MAAM;AAAA,QACb;AAAA,MACF;AAAA,MACA,UAAU,UAAU;AAClB,aAAK,OAAO,SAAS;AAAA,MACvB;AAAA,MACA,YAAY;AACV,YAAI,KAAK,OAAO,2BAA2B,SAAS,4BAA4B,KAAK,QAAQ;AAC3F,eAAK,OAAO,wBAAwB;AAAA,QACtC;AAAA,MACF;AAAA,MACA,aAAa;AACX,YAAI,SAAS,wBAAwB,SAAS,4BAA4B,KAAK,QAAQ;AACrF,mBAAS,qBAAqB;AAAA,QAChC;AAAA,MACF;AAAA,MACA,gBAAgB,MAAM;AACpB,YAAI;AACF,eAAK,OAAO,eAAe;AAAA,QAC7B,SAAS,OAAO;AACd,eAAK,MAAM,QAAQ,KAAK;AAAA,QAC1B;AAAA,MACF;AAAA,MACA,cAAc;AACZ,YAAI,CAAC,KAAK;AACR,iBAAO;AACT,cAAM,EAAE,UAAU,SAAS,IAAI,KAAK;AACpC,YAAI,aAAa,YAAY,SAAS,SAAS,GAAG;AAChD,iBAAO,SAAS,IAAI,SAAS,SAAS,CAAC;AAAA,QACzC;AACA,eAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AACf,YAAI,CAAC,KAAK;AACR,iBAAO;AACT,eAAO,KAAK,OAAO;AAAA,MACrB;AAAA,MACA,mBAAmB;AACjB,YAAI,CAAC,KAAK;AACR,iBAAO;AACT,cAAM,EAAE,SAAS,IAAI,KAAK;AAC1B,YAAI,SAAS,WAAW,GAAG;AACzB,iBAAO;AAAA,QACT;AACA,cAAM,MAAM,SAAS,IAAI,SAAS,SAAS,CAAC;AAC5C,cAAM,WAAW,KAAK,YAAY;AAClC,YAAI,MAAM,UAAU;AAClB,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAAA,MACA,cAAc,KAAK;AACjB,cAAM,CAAC,EAAE,EAAE,IAAI,IAAI,MAAM,gBAAgB,aAAa;AACtD,eAAO;AAAA,MACT;AAAA,MACA,SAAS;AACP,cAAM,EAAE,KAAK,SAAS,MAAM,UAAU,OAAO,QAAQ,OAAO,OAAO,IAAI,KAAK;AAC5E,cAAM,QAAQ;AAAA,UACZ,OAAO,UAAU,SAAS,QAAQ;AAAA,UAClC,QAAQ,WAAW,SAAS,SAAS;AAAA,QACvC;AACA,YAAI,aAAa,OAAO;AACtB,gBAAM,YAAY,IAAI;AAAA,QACxB;AACA,eAAuB,aAAa,QAAQ;AAAA,UAC1C;AAAA,UACA;AAAA,YACE,KAAK,KAAK;AAAA,YACV,eAAe,KAAK,cAAc,GAAG;AAAA,YACrC;AAAA,YACA,SAAS;AAAA,YACT,UAAU,WAAW;AAAA,YACrB,OAAO,QAAQ,KAAK;AAAA,YACpB,MAAM,OAAO,KAAK;AAAA,YAClB,GAAG,OAAO;AAAA,UACZ;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,kBAAc,KAAK,eAAe,KAAK;AACvC,kBAAc,KAAK,WAAW,gBAAgB,QAAQ,GAAG;AAAA;AAAA;", "names": []}