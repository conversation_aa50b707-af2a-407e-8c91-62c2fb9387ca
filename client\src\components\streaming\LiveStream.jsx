import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  IconButton,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  TextField,
  Badge,
  Tooltip,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Card,
  CardContent
} from '@mui/material';
import {
  Videocam as VideocamIcon,
  VideocamOff as VideocamOffIcon,
  Mic as MicIcon,
  MicOff as MicOffIcon,
  ScreenShare as ScreenShareIcon,
  StopScreenShare as StopScreenShareIcon,
  Chat as ChatIcon,
  People as PeopleIcon,
  Settings as SettingsIcon,
  Fullscreen as FullscreenIcon,
  Record as RecordIcon,
  Stop as StopIcon,
  Send as SendIcon,
  RaiseHand as RaiseHandIcon,
  ThumbUp as ThumbUpIcon
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';
import { io } from 'socket.io-client';

const LiveStream = ({ 
  streamId, 
  isInstructor = false, 
  courseId,
  onStreamEnd 
}) => {
  const { user } = useAuth();
  const [isStreaming, setIsStreaming] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isCameraOn, setIsCameraOn] = useState(true);
  const [isMicOn, setIsMicOn] = useState(true);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [showChat, setShowChat] = useState(true);
  const [showParticipants, setShowParticipants] = useState(false);
  const [participants, setParticipants] = useState([]);
  const [chatMessages, setChatMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [reactions, setReactions] = useState([]);
  const [raisedHands, setRaisedHands] = useState([]);
  const [socket, setSocket] = useState(null);
  const [settingsOpen, setSettingsOpen] = useState(false);

  const videoRef = useRef(null);
  const streamRef = useRef(null);
  const recordingRef = useRef(null);

  // Initialize WebRTC and Socket.IO
  useEffect(() => {
    const newSocket = io(import.meta.env.VITE_API_URL || 'http://localhost:5000');
    setSocket(newSocket);

    // Join stream room
    newSocket.emit('join-stream', { streamId, user });

    // Listen for participants
    newSocket.on('participants-updated', (participantsList) => {
      setParticipants(participantsList);
    });

    // Listen for chat messages
    newSocket.on('stream-message', (message) => {
      setChatMessages(prev => [...prev, message]);
    });

    // Listen for reactions
    newSocket.on('stream-reaction', (reaction) => {
      setReactions(prev => [...prev, reaction]);
      // Remove reaction after 3 seconds
      setTimeout(() => {
        setReactions(prev => prev.filter(r => r.id !== reaction.id));
      }, 3000);
    });

    // Listen for raised hands
    newSocket.on('hand-raised', (data) => {
      setRaisedHands(prev => [...prev, data]);
    });

    newSocket.on('hand-lowered', (data) => {
      setRaisedHands(prev => prev.filter(h => h.userId !== data.userId));
    });

    return () => {
      newSocket.close();
      stopStream();
    };
  }, [streamId, user]);

  // Start camera stream
  const startStream = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: isCameraOn,
        audio: isMicOn
      });

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }

      streamRef.current = stream;
      setIsStreaming(true);

      // Notify other participants
      socket?.emit('stream-started', { streamId, user });
    } catch (error) {
      console.error('Error starting stream:', error);
      alert('Failed to start stream. Please check camera and microphone permissions.');
    }
  };

  // Stop stream
  const stopStream = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }

    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }

    setIsStreaming(false);
    setIsScreenSharing(false);
    stopRecording();

    // Notify other participants
    socket?.emit('stream-ended', { streamId, user });
    onStreamEnd?.();
  };

  // Toggle camera
  const toggleCamera = () => {
    if (streamRef.current) {
      const videoTrack = streamRef.current.getVideoTracks()[0];
      if (videoTrack) {
        videoTrack.enabled = !isCameraOn;
        setIsCameraOn(!isCameraOn);
      }
    }
  };

  // Toggle microphone
  const toggleMic = () => {
    if (streamRef.current) {
      const audioTrack = streamRef.current.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !isMicOn;
        setIsMicOn(!isMicOn);
      }
    }
  };

  // Start screen sharing
  const startScreenShare = async () => {
    try {
      const screenStream = await navigator.mediaDevices.getDisplayMedia({
        video: true,
        audio: true
      });

      if (videoRef.current) {
        videoRef.current.srcObject = screenStream;
      }

      // Replace video track
      if (streamRef.current) {
        const videoTrack = streamRef.current.getVideoTracks()[0];
        const screenTrack = screenStream.getVideoTracks()[0];
        
        if (videoTrack) {
          streamRef.current.removeTrack(videoTrack);
          videoTrack.stop();
        }
        
        streamRef.current.addTrack(screenTrack);
      }

      setIsScreenSharing(true);

      // Handle screen share end
      screenStream.getVideoTracks()[0].onended = () => {
        stopScreenShare();
      };
    } catch (error) {
      console.error('Error starting screen share:', error);
    }
  };

  // Stop screen sharing
  const stopScreenShare = async () => {
    try {
      // Get camera stream again
      const cameraStream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: false
      });

      if (videoRef.current) {
        videoRef.current.srcObject = streamRef.current;
      }

      // Replace screen track with camera track
      if (streamRef.current) {
        const screenTrack = streamRef.current.getVideoTracks()[0];
        const cameraTrack = cameraStream.getVideoTracks()[0];
        
        if (screenTrack) {
          streamRef.current.removeTrack(screenTrack);
          screenTrack.stop();
        }
        
        streamRef.current.addTrack(cameraTrack);
      }

      setIsScreenSharing(false);
    } catch (error) {
      console.error('Error stopping screen share:', error);
    }
  };

  // Start recording
  const startRecording = () => {
    if (streamRef.current) {
      const mediaRecorder = new MediaRecorder(streamRef.current);
      const chunks = [];

      mediaRecorder.ondataavailable = (event) => {
        chunks.push(event.data);
      };

      mediaRecorder.onstop = () => {
        const blob = new Blob(chunks, { type: 'video/webm' });
        const url = URL.createObjectURL(blob);
        
        // Create download link
        const a = document.createElement('a');
        a.href = url;
        a.download = `stream-${streamId}-${Date.now()}.webm`;
        a.click();
        
        URL.revokeObjectURL(url);
      };

      mediaRecorder.start();
      recordingRef.current = mediaRecorder;
      setIsRecording(true);
    }
  };

  // Stop recording
  const stopRecording = () => {
    if (recordingRef.current && isRecording) {
      recordingRef.current.stop();
      recordingRef.current = null;
      setIsRecording(false);
    }
  };

  // Send chat message
  const sendMessage = () => {
    if (newMessage.trim() && socket) {
      const message = {
        id: Date.now(),
        text: newMessage,
        user: {
          id: user.id,
          name: `${user.firstName} ${user.lastName}`,
          avatar: user.avatar
        },
        timestamp: new Date()
      };

      socket.emit('stream-message', { streamId, message });
      setNewMessage('');
    }
  };

  // Send reaction
  const sendReaction = (emoji) => {
    if (socket) {
      const reaction = {
        id: Date.now(),
        emoji,
        user: {
          id: user.id,
          name: `${user.firstName} ${user.lastName}`
        }
      };

      socket.emit('stream-reaction', { streamId, reaction });
    }
  };

  // Raise/lower hand
  const toggleRaiseHand = () => {
    const isHandRaised = raisedHands.some(h => h.userId === user.id);
    
    if (isHandRaised) {
      socket?.emit('lower-hand', { streamId, userId: user.id });
    } else {
      socket?.emit('raise-hand', { 
        streamId, 
        userId: user.id, 
        userName: `${user.firstName} ${user.lastName}` 
      });
    }
  };

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Paper sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box display="flex" alignItems="center" gap={2}>
          <Typography variant="h6">Live Stream</Typography>
          {isStreaming && (
            <Chip
              label="LIVE"
              color="error"
              size="small"
              sx={{ animation: 'pulse 2s infinite' }}
            />
          )}
          <Chip
            label={`${participants.length} participants`}
            variant="outlined"
            size="small"
          />
        </Box>

        <Box display="flex" gap={1}>
          {/* Reactions */}
          <Button
            size="small"
            onClick={() => sendReaction('👍')}
            startIcon={<ThumbUpIcon />}
          >
            Like
          </Button>

          {/* Raise Hand */}
          <Button
            size="small"
            onClick={toggleRaiseHand}
            startIcon={<RaiseHandIcon />}
            color={raisedHands.some(h => h.userId === user.id) ? 'primary' : 'inherit'}
          >
            {raisedHands.some(h => h.userId === user.id) ? 'Lower Hand' : 'Raise Hand'}
          </Button>

          {/* Settings */}
          <IconButton onClick={() => setSettingsOpen(true)}>
            <SettingsIcon />
          </IconButton>
        </Box>
      </Paper>

      {/* Main Content */}
      <Box sx={{ flex: 1, display: 'flex' }}>
        {/* Video Area */}
        <Box sx={{ flex: 1, position: 'relative', bgcolor: 'black' }}>
          <video
            ref={videoRef}
            autoPlay
            muted={isInstructor}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover'
            }}
          />

          {/* Reactions Overlay */}
          <Box
            sx={{
              position: 'absolute',
              top: 20,
              right: 20,
              display: 'flex',
              flexDirection: 'column',
              gap: 1
            }}
          >
            {reactions.map((reaction) => (
              <Box
                key={reaction.id}
                sx={{
                  fontSize: '2rem',
                  animation: 'fadeInOut 3s ease-in-out'
                }}
              >
                {reaction.emoji}
              </Box>
            ))}
          </Box>

          {/* Controls */}
          {isInstructor && (
            <Box
              sx={{
                position: 'absolute',
                bottom: 20,
                left: '50%',
                transform: 'translateX(-50%)',
                display: 'flex',
                gap: 1,
                bgcolor: 'rgba(0,0,0,0.7)',
                borderRadius: 2,
                p: 1
              }}
            >
              {!isStreaming ? (
                <Button
                  variant="contained"
                  color="primary"
                  onClick={startStream}
                  startIcon={<VideocamIcon />}
                >
                  Start Stream
                </Button>
              ) : (
                <>
                  <IconButton
                    onClick={toggleCamera}
                    color={isCameraOn ? 'primary' : 'error'}
                  >
                    {isCameraOn ? <VideocamIcon /> : <VideocamOffIcon />}
                  </IconButton>

                  <IconButton
                    onClick={toggleMic}
                    color={isMicOn ? 'primary' : 'error'}
                  >
                    {isMicOn ? <MicIcon /> : <MicOffIcon />}
                  </IconButton>

                  <IconButton
                    onClick={isScreenSharing ? stopScreenShare : startScreenShare}
                    color={isScreenSharing ? 'primary' : 'inherit'}
                  >
                    {isScreenSharing ? <StopScreenShareIcon /> : <ScreenShareIcon />}
                  </IconButton>

                  <IconButton
                    onClick={isRecording ? stopRecording : startRecording}
                    color={isRecording ? 'error' : 'inherit'}
                  >
                    {isRecording ? <StopIcon /> : <RecordIcon />}
                  </IconButton>

                  <Button
                    variant="contained"
                    color="error"
                    onClick={stopStream}
                    startIcon={<StopIcon />}
                  >
                    End Stream
                  </Button>
                </>
              )}
            </Box>
          )}
        </Box>

        {/* Sidebar */}
        <Box sx={{ width: 350, borderLeft: 1, borderColor: 'divider' }}>
          {/* Tabs */}
          <Box sx={{ display: 'flex', borderBottom: 1, borderColor: 'divider' }}>
            <Button
              fullWidth
              variant={showChat ? 'contained' : 'text'}
              onClick={() => { setShowChat(true); setShowParticipants(false); }}
              startIcon={<ChatIcon />}
            >
              Chat
            </Button>
            <Button
              fullWidth
              variant={showParticipants ? 'contained' : 'text'}
              onClick={() => { setShowChat(false); setShowParticipants(true); }}
              startIcon={
                <Badge badgeContent={raisedHands.length} color="error">
                  <PeopleIcon />
                </Badge>
              }
            >
              People
            </Button>
          </Box>

          {/* Chat */}
          {showChat && (
            <Box sx={{ height: 'calc(100vh - 200px)', display: 'flex', flexDirection: 'column' }}>
              <List sx={{ flex: 1, overflow: 'auto', p: 1 }}>
                {chatMessages.map((message) => (
                  <ListItem key={message.id} alignItems="flex-start">
                    <ListItemAvatar>
                      <Avatar src={message.user.avatar}>
                        {message.user.name.charAt(0)}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={message.user.name}
                      secondary={message.text}
                    />
                  </ListItem>
                ))}
              </List>

              <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
                <TextField
                  fullWidth
                  size="small"
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                  placeholder="Type a message..."
                  InputProps={{
                    endAdornment: (
                      <IconButton onClick={sendMessage} disabled={!newMessage.trim()}>
                        <SendIcon />
                      </IconButton>
                    )
                  }}
                />
              </Box>
            </Box>
          )}

          {/* Participants */}
          {showParticipants && (
            <Box sx={{ height: 'calc(100vh - 200px)', overflow: 'auto', p: 1 }}>
              {/* Raised Hands */}
              {raisedHands.length > 0 && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" color="error" gutterBottom>
                    Raised Hands ({raisedHands.length})
                  </Typography>
                  {raisedHands.map((hand) => (
                    <Chip
                      key={hand.userId}
                      label={hand.userName}
                      color="error"
                      variant="outlined"
                      sx={{ mr: 1, mb: 1 }}
                      icon={<RaiseHandIcon />}
                    />
                  ))}
                </Box>
              )}

              {/* All Participants */}
              <Typography variant="subtitle2" gutterBottom>
                Participants ({participants.length})
              </Typography>
              <List>
                {participants.map((participant) => (
                  <ListItem key={participant.id}>
                    <ListItemAvatar>
                      <Avatar src={participant.avatar}>
                        {participant.name.charAt(0)}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={participant.name}
                      secondary={participant.role}
                    />
                  </ListItem>
                ))}
              </List>
            </Box>
          )}
        </Box>
      </Box>

      {/* Settings Dialog */}
      <Dialog open={settingsOpen} onClose={() => setSettingsOpen(false)}>
        <DialogTitle>Stream Settings</DialogTitle>
        <DialogContent>
          <Typography>Stream settings would go here</Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSettingsOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      <style jsx>{`
        @keyframes pulse {
          0% { opacity: 1; }
          50% { opacity: 0.5; }
          100% { opacity: 1; }
        }
        
        @keyframes fadeInOut {
          0% { opacity: 0; transform: translateY(20px); }
          20% { opacity: 1; transform: translateY(0); }
          80% { opacity: 1; transform: translateY(0); }
          100% { opacity: 0; transform: translateY(-20px); }
        }
      `}</style>
    </Box>
  );
};

export default LiveStream;
