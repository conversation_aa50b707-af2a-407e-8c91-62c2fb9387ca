const express = require('express');
const Assessment = require('../models/Assessment');
const Course = require('../models/Course');
const Progress = require('../models/Progress');
const { auth, instructorAuth, studentAuth } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/assessments/course/:courseId
// @desc    Get all assessments for a course
// @access  Private
router.get('/course/:courseId', auth, async (req, res) => {
  try {
    const course = await Course.findById(req.params.courseId);
    
    if (!course) {
      return res.status(404).json({
        message: 'Course not found'
      });
    }

    // Check if user has access to this course
    const isInstructor = course.instructor.toString() === req.user.id;
    const isEnrolled = course.enrolledStudents.some(
      enrollment => enrollment.student.toString() === req.user.id
    );
    const isAdmin = req.user.role === 'admin';

    if (!isInstructor && !isEnrolled && !isAdmin) {
      return res.status(403).json({
        message: 'Access denied'
      });
    }

    let query = { course: req.params.courseId };
    
    // Students can only see published assessments
    if (req.user.role === 'student') {
      query.isPublished = true;
      query.availableFrom = { $lte: new Date() };
      query.$or = [
        { availableUntil: { $gte: new Date() } },
        { availableUntil: null }
      ];
    }

    const assessments = await Assessment.find(query)
      .populate('instructor', 'firstName lastName')
      .sort({ createdAt: -1 });

    res.json({
      success: true,
      assessments
    });
  } catch (error) {
    console.error('Get assessments error:', error);
    res.status(500).json({
      message: 'Server error getting assessments',
      error: error.message
    });
  }
});

// @route   GET /api/assessments/:id
// @desc    Get single assessment
// @access  Private
router.get('/:id', auth, async (req, res) => {
  try {
    const assessment = await Assessment.findById(req.params.id)
      .populate('course', 'title instructor')
      .populate('instructor', 'firstName lastName');

    if (!assessment) {
      return res.status(404).json({
        message: 'Assessment not found'
      });
    }

    // Check access permissions
    const isInstructor = assessment.instructor._id.toString() === req.user.id;
    const isEnrolled = await Course.findOne({
      _id: assessment.course._id,
      'enrolledStudents.student': req.user.id
    });
    const isAdmin = req.user.role === 'admin';

    if (!isInstructor && !isEnrolled && !isAdmin) {
      return res.status(403).json({
        message: 'Access denied'
      });
    }

    // For students, check if assessment is available
    if (req.user.role === 'student') {
      if (!assessment.isPublished) {
        return res.status(403).json({
          message: 'Assessment is not published yet'
        });
      }

      const now = new Date();
      if (assessment.availableFrom > now) {
        return res.status(403).json({
          message: 'Assessment is not available yet'
        });
      }

      if (assessment.availableUntil && assessment.availableUntil < now) {
        return res.status(403).json({
          message: 'Assessment is no longer available'
        });
      }
    }

    // For students, don't send correct answers unless configured to show them
    if (req.user.role === 'student' && !assessment.showCorrectAnswers) {
      assessment.questions.forEach(question => {
        if (question.type === 'multiple-choice' || question.type === 'true-false') {
          question.options.forEach(option => {
            option.isCorrect = undefined;
          });
        }
        question.correctAnswer = undefined;
        question.explanation = undefined;
      });
    }

    res.json({
      success: true,
      assessment
    });
  } catch (error) {
    console.error('Get assessment error:', error);
    res.status(500).json({
      message: 'Server error getting assessment',
      error: error.message
    });
  }
});

// @route   POST /api/assessments
// @desc    Create new assessment
// @access  Private/Instructor
router.post('/', auth, instructorAuth, async (req, res) => {
  try {
    const {
      title,
      description,
      course,
      type,
      questions,
      timeLimit,
      attempts,
      passingScore,
      dueDate,
      availableFrom,
      availableUntil,
      shuffleQuestions,
      showCorrectAnswers,
      showScoreImmediately
    } = req.body;

    // Validation
    if (!title || !description || !course || !type || !questions || !passingScore) {
      return res.status(400).json({
        message: 'Please provide all required fields'
      });
    }

    // Check if course exists and user is instructor
    const courseDoc = await Course.findById(course);
    if (!courseDoc) {
      return res.status(404).json({
        message: 'Course not found'
      });
    }

    if (courseDoc.instructor.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({
        message: 'Access denied. You can only create assessments for your own courses.'
      });
    }

    const assessment = await Assessment.create({
      title,
      description,
      course,
      instructor: req.user.id,
      type,
      questions,
      timeLimit,
      attempts: attempts || 1,
      passingScore,
      dueDate,
      availableFrom: availableFrom || new Date(),
      availableUntil,
      shuffleQuestions: shuffleQuestions || false,
      showCorrectAnswers: showCorrectAnswers !== undefined ? showCorrectAnswers : true,
      showScoreImmediately: showScoreImmediately !== undefined ? showScoreImmediately : true
    });

    res.status(201).json({
      success: true,
      message: 'Assessment created successfully',
      assessment
    });
  } catch (error) {
    console.error('Create assessment error:', error);
    res.status(500).json({
      message: 'Server error creating assessment',
      error: error.message
    });
  }
});

// @route   PUT /api/assessments/:id
// @desc    Update assessment
// @access  Private/Instructor
router.put('/:id', auth, instructorAuth, async (req, res) => {
  try {
    const assessment = await Assessment.findById(req.params.id);

    if (!assessment) {
      return res.status(404).json({
        message: 'Assessment not found'
      });
    }

    // Check if user is the instructor
    if (assessment.instructor.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({
        message: 'Access denied'
      });
    }

    const {
      title,
      description,
      questions,
      timeLimit,
      attempts,
      passingScore,
      dueDate,
      availableFrom,
      availableUntil,
      shuffleQuestions,
      showCorrectAnswers,
      showScoreImmediately,
      isPublished
    } = req.body;

    // Update fields
    if (title) assessment.title = title;
    if (description) assessment.description = description;
    if (questions) assessment.questions = questions;
    if (timeLimit !== undefined) assessment.timeLimit = timeLimit;
    if (attempts) assessment.attempts = attempts;
    if (passingScore !== undefined) assessment.passingScore = passingScore;
    if (dueDate !== undefined) assessment.dueDate = dueDate;
    if (availableFrom) assessment.availableFrom = availableFrom;
    if (availableUntil !== undefined) assessment.availableUntil = availableUntil;
    if (shuffleQuestions !== undefined) assessment.shuffleQuestions = shuffleQuestions;
    if (showCorrectAnswers !== undefined) assessment.showCorrectAnswers = showCorrectAnswers;
    if (showScoreImmediately !== undefined) assessment.showScoreImmediately = showScoreImmediately;
    if (isPublished !== undefined) assessment.isPublished = isPublished;

    await assessment.save();

    res.json({
      success: true,
      message: 'Assessment updated successfully',
      assessment
    });
  } catch (error) {
    console.error('Update assessment error:', error);
    res.status(500).json({
      message: 'Server error updating assessment',
      error: error.message
    });
  }
});

// @route   DELETE /api/assessments/:id
// @desc    Delete assessment
// @access  Private/Instructor
router.delete('/:id', auth, instructorAuth, async (req, res) => {
  try {
    const assessment = await Assessment.findById(req.params.id);

    if (!assessment) {
      return res.status(404).json({
        message: 'Assessment not found'
      });
    }

    // Check if user is the instructor
    if (assessment.instructor.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({
        message: 'Access denied'
      });
    }

    await Assessment.findByIdAndDelete(req.params.id);

    res.json({
      success: true,
      message: 'Assessment deleted successfully'
    });
  } catch (error) {
    console.error('Delete assessment error:', error);
    res.status(500).json({
      message: 'Server error deleting assessment',
      error: error.message
    });
  }
});

// @route   POST /api/assessments/:id/submit
// @desc    Submit assessment answers
// @access  Private/Student
router.post('/:id/submit', auth, studentAuth, async (req, res) => {
  try {
    const assessment = await Assessment.findById(req.params.id);

    if (!assessment) {
      return res.status(404).json({
        message: 'Assessment not found'
      });
    }

    // Check if student is enrolled in the course
    const course = await Course.findById(assessment.course);
    const isEnrolled = course.enrolledStudents.some(
      enrollment => enrollment.student.toString() === req.user.id
    );

    if (!isEnrolled) {
      return res.status(403).json({
        message: 'You must be enrolled in the course to submit this assessment'
      });
    }

    // Check if assessment is available
    const now = new Date();
    if (!assessment.isPublished || assessment.availableFrom > now) {
      return res.status(403).json({
        message: 'Assessment is not available'
      });
    }

    if (assessment.availableUntil && assessment.availableUntil < now) {
      return res.status(403).json({
        message: 'Assessment deadline has passed'
      });
    }

    // Get student's progress
    let progress = await Progress.findOne({
      student: req.user.id,
      course: assessment.course
    });

    if (!progress) {
      return res.status(404).json({
        message: 'Progress record not found'
      });
    }

    // Check attempt limit
    const previousSubmissions = progress.assessmentSubmissions.filter(
      sub => sub.assessment.toString() === assessment._id.toString()
    );

    if (previousSubmissions.length >= assessment.attempts) {
      return res.status(400).json({
        message: 'Maximum attempts exceeded'
      });
    }

    const { answers, timeSpent } = req.body;

    if (!answers || !Array.isArray(answers)) {
      return res.status(400).json({
        message: 'Please provide answers'
      });
    }

    // Grade the submission
    let totalPoints = 0;
    let earnedPoints = 0;
    const gradedAnswers = [];

    assessment.questions.forEach((question, index) => {
      totalPoints += question.points;
      const studentAnswer = answers.find(a => a.questionId === question._id.toString());

      let isCorrect = false;
      let pointsEarned = 0;

      if (studentAnswer) {
        if (question.type === 'multiple-choice') {
          const correctOption = question.options.find(opt => opt.isCorrect);
          isCorrect = studentAnswer.answer === correctOption?.text;
        } else if (question.type === 'true-false') {
          const correctOption = question.options.find(opt => opt.isCorrect);
          isCorrect = studentAnswer.answer === correctOption?.text;
        } else if (question.type === 'short-answer') {
          // Simple string comparison (case-insensitive)
          isCorrect = studentAnswer.answer?.toLowerCase().trim() ===
                     question.correctAnswer?.toLowerCase().trim();
        }
        // Essay questions need manual grading

        if (isCorrect) {
          pointsEarned = question.points;
        }
      }

      earnedPoints += pointsEarned;
      gradedAnswers.push({
        questionId: question._id,
        answer: studentAnswer?.answer || '',
        isCorrect,
        pointsEarned
      });
    });

    const percentage = totalPoints > 0 ? Math.round((earnedPoints / totalPoints) * 100) : 0;
    const passed = percentage >= assessment.passingScore;

    // Create submission record
    const submission = {
      assessment: assessment._id,
      answers: gradedAnswers,
      score: earnedPoints,
      percentage,
      passed,
      timeSpent: timeSpent || 0,
      attemptNumber: previousSubmissions.length + 1,
      submittedAt: new Date()
    };

    progress.assessmentSubmissions.push(submission);
    await progress.save();

    res.json({
      success: true,
      message: 'Assessment submitted successfully',
      result: {
        score: earnedPoints,
        totalPoints,
        percentage,
        passed,
        showCorrectAnswers: assessment.showCorrectAnswers,
        answers: assessment.showCorrectAnswers ? gradedAnswers : undefined
      }
    });
  } catch (error) {
    console.error('Submit assessment error:', error);
    res.status(500).json({
      message: 'Server error submitting assessment',
      error: error.message
    });
  }
});

// @route   GET /api/assessments/:id/submissions
// @desc    Get assessment submissions (instructor only)
// @access  Private/Instructor
router.get('/:id/submissions', auth, instructorAuth, async (req, res) => {
  try {
    const assessment = await Assessment.findById(req.params.id);

    if (!assessment) {
      return res.status(404).json({
        message: 'Assessment not found'
      });
    }

    // Check if user is the instructor
    if (assessment.instructor.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({
        message: 'Access denied'
      });
    }

    const submissions = await Progress.find({
      course: assessment.course,
      'assessmentSubmissions.assessment': assessment._id
    })
    .populate('student', 'firstName lastName email')
    .select('student assessmentSubmissions');

    // Filter and format submissions
    const formattedSubmissions = [];
    submissions.forEach(progress => {
      const studentSubmissions = progress.assessmentSubmissions.filter(
        sub => sub.assessment.toString() === assessment._id.toString()
      );

      studentSubmissions.forEach(submission => {
        formattedSubmissions.push({
          student: progress.student,
          ...submission.toObject()
        });
      });
    });

    res.json({
      success: true,
      submissions: formattedSubmissions
    });
  } catch (error) {
    console.error('Get submissions error:', error);
    res.status(500).json({
      message: 'Server error getting submissions',
      error: error.message
    });
  }
});

module.exports = router;
