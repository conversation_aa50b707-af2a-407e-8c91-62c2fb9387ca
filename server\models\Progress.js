const mongoose = require('mongoose');

const submissionSchema = new mongoose.Schema({
  assessment: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Assessment',
    required: true
  },
  answers: [{
    questionId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true
    },
    answer: mongoose.Schema.Types.Mixed, // Can be string, array, or object
    isCorrect: {
      type: Boolean,
      default: false
    },
    pointsEarned: {
      type: Number,
      default: 0
    }
  }],
  score: {
    type: Number,
    required: true,
    min: 0
  },
  percentage: {
    type: Number,
    required: true,
    min: 0,
    max: 100
  },
  passed: {
    type: Boolean,
    required: true
  },
  timeSpent: {
    type: Number, // in minutes
    default: 0
  },
  attemptNumber: {
    type: Number,
    required: true,
    min: 1
  },
  submittedAt: {
    type: Date,
    default: Date.now
  },
  gradedAt: {
    type: Date
  },
  feedback: {
    type: String,
    maxlength: [1000, 'Feedback cannot exceed 1000 characters']
  }
});

const lessonProgressSchema = new mongoose.Schema({
  lesson: {
    type: mongoose.Schema.Types.ObjectId,
    required: true
  },
  completed: {
    type: Boolean,
    default: false
  },
  completedAt: {
    type: Date
  },
  timeSpent: {
    type: Number, // in minutes
    default: 0
  },
  lastWatchedPosition: {
    type: Number, // in seconds
    default: 0
  }
});

const progressSchema = new mongoose.Schema({
  student: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  course: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Course',
    required: true
  },
  enrolledAt: {
    type: Date,
    default: Date.now
  },
  lastAccessedAt: {
    type: Date,
    default: Date.now
  },
  completionPercentage: {
    type: Number,
    default: 0,
    min: 0,
    max: 100
  },
  completedAt: {
    type: Date
  },
  totalTimeSpent: {
    type: Number, // in minutes
    default: 0
  },
  lessonsProgress: [lessonProgressSchema],
  assessmentSubmissions: [submissionSchema],
  certificateIssued: {
    type: Boolean,
    default: false
  },
  certificateIssuedAt: {
    type: Date
  },
  notes: [{
    lesson: {
      type: mongoose.Schema.Types.ObjectId
    },
    content: {
      type: String,
      required: true,
      maxlength: [1000, 'Note cannot exceed 1000 characters']
    },
    timestamp: {
      type: Number, // video timestamp in seconds
      default: 0
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],
  bookmarks: [{
    lesson: {
      type: mongoose.Schema.Types.ObjectId,
      required: true
    },
    timestamp: {
      type: Number, // video timestamp in seconds
      required: true
    },
    title: {
      type: String,
      required: true,
      maxlength: [100, 'Bookmark title cannot exceed 100 characters']
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  }]
}, {
  timestamps: true
});

// Compound indexes for better query performance
progressSchema.index({ student: 1, course: 1 }, { unique: true });
progressSchema.index({ course: 1 });
progressSchema.index({ student: 1 });
progressSchema.index({ completionPercentage: 1 });

// Update completion percentage before saving
progressSchema.pre('save', function(next) {
  if (this.lessonsProgress && this.lessonsProgress.length > 0) {
    const completedLessons = this.lessonsProgress.filter(lesson => lesson.completed).length;
    this.completionPercentage = Math.round((completedLessons / this.lessonsProgress.length) * 100);
    
    // Mark course as completed if all lessons are done
    if (this.completionPercentage === 100 && !this.completedAt) {
      this.completedAt = new Date();
    }
  }
  next();
});

module.exports = mongoose.model('Progress', progressSchema);
